#!/bin/bash

# Kubernetes STIG Compliance Check Script
# Version: 2.1
# Date: $(date +%Y-%m-%d)

REPORT_FILE="k8s_stig_report_$(date +%Y%m%d%H%M).html"
FAILED=0
PASSED=0

# Initialize HTML Report
cat > $REPORT_FILE << EOF
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Kubernetes STIG Compliance Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        h1 { color: #2c3e50; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        tr:hover { background-color: #f5f5f5; }
        .passed { color: #27ae60; font-weight: bold; }
        .failed { color: #c0392b; font-weight: bold; }
        .severity-high { background-color: #f8d7da; }
        .severity-MEDIUM { background-color: #fff3cd; }
        .severity-low { background-color: #d4edda; }
        .summary { margin-top: 30px; padding: 20px; background-color: #f8f9fa; border-radius: 5px; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>Kubernetes STIG Compliance Report</h1>
    <p>Benchmark Name: Kubernetes</p>
    <p>Benchmark ID: Kubernetes_STIG</p>
    <p>Benchmark Date: 02 Apr 2025</p>
    <p>Benchmark Release: 3 </p>
    <p>Generated at: $(date -u +"%Y-%m-%dT%H:%M:%SZ")</p>
    <table>
        <thead>
            <tr>
                <th>Check ID</th>
                <th>Title</th>
                <th>Severity</th>
                <th>Result</th>
                <th>Details</th>
            </tr>
        </thead>
        <tbody>
EOF

add_report_row() {
    local check_id=$1
    local title=$2
    local severity=$3
    local result=$4
    local details=$5

    local severity_class=""
    case $severity in
        HIGH) severity_class="severity-high";;
        MEDIUM) severity_class="severity-MEDIUM";;
        WARN) severity_class="severity-low";;
    esac

    details=$(echo "$details" | sed 's/</\&lt;/g; s/>/\&gt;/g')
    details=${details//$'\n'/<br>}

    cat >> $REPORT_FILE << EOF
            <tr class="$severity_class">
                <td>$check_id</td>
                <td>$title</td>
                <td>$severity</td>
                <td class="${result,,}">$result</td>
                <td>$details</td>
            </tr>
EOF
}

if_tools() {
   echo "[tools check!]"
    if command -v jq >/dev/null 2>&1; then
      echo 'jq is exists'
    else
      echo 'jq is not exists,'
#      docker run -it -d --name tool-set  152-231-registry.alauda.cn:60080/ops/toolset:latest bash >/dev/null 2>&1
#      docker cp tool-set:/usr/local/bin/jq /bin/jq

      cp ./jq /bin/
      chmod +x /bin/jq
fi
    if command -v yq >/dev/null 2>&1; then
      echo 'yq is exists'
    else
      echo 'yq is not exists,'
#      docker run -it -d --name tool-set  152-231-registry.alauda.cn:60080/ops/toolset:latest bash >/dev/null 2>&1
#      docker cp tool-set:/usr/local/bin/yq /bin/yq
      cp ./yq /bin/
      chmod +x /bin/yq
fi
}

check_v242415() {
    local check_id="V-242415"
    local title="Secrets in Kubernetes must not be stored as environment variables."
    local severity="HIGH"
    local result="PASSED"
    local details=""
    local resources=$(kubectl get all -A -o jsonpath='{range .items[?(@..secretKeyRef)]}{.kind}/{.metadata.namespace}/{.metadata.name}{"\n"}{end}')

    if [ -n "$resources" ]; then
        result="FAILED"
        details="Found resources with secretKeyRef:<br>"
        while read -r resource; do
            details+="- $resource<br>"
        done <<< "$resources"
        ((FAILED++))
    else
        details="No secretKeyRef references found"
        ((PASSED++))
    fi

    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242397() {
    local check_id="V-242397"
    local title="The Kubernetes kubelet staticPodPath must not enable static pods."
    local severity="HIGH"
    local result="PASSED"
    local details=""
    local config_file="/var/lib/kubelet/config.yaml"

    if [ -f "$config_file" ]; then
        static_path=$(grep 'staticPodPath' "$config_file")
        if [ -n "$static_path" ]; then
            result="FAILED"
            details="Static pod path configured: $static_path"
            ((FAILED++))
        else
            details="No static pod path configured"
            ((PASSED++))
        fi
    else
        result="FAILED"
        details="Kubelet config file missing"
        ((FAILED++))
    fi

    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242392() {
    local check_id="V-242392"
    local title="The Kubernetes kubelet must enable explicit authorization."
    local severity="HIGH"
    local result="PASSED"
    local details=""
    local CONFIG_FILE="/var/lib/kubelet/config.yaml"

    MODE=$(yq '.authorization.mode' "$CONFIG_FILE"|tr -d '"')
    if [[ $MODE == "Webhook" ]]; then
        details="Authorization mode correctly set to Webhook"
        ((PASSED++))

    elif [[ -z "$MODE" ]]; then
        result="FAILED"
        details="Authorization mode not configured (authorization. mode field missing)"
        ((FAILED++))
    else
        result="FAILED"
        details="Authorization mode does not meet requirements (current value: $MODE, required value: Webhook)"
        ((FAILED++))
    fi

    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242381() {
    local check_id="V-242381"
    local title="The Kubernetes Controller Manager must create unique service accounts for each work payload."
    local severity="HIGH"
    local result="PASSED"
    local details=""
    local manifest_path="/etc/kubernetes/manifests/kube-controller-manager.yaml"

    if [ ! -f "$manifest_path" ]; then
        result="FAILED"
        details="Controller Manager manifest missing"
        ((FAILED++))
    else
        if ! grep -q -- "--use-service-account-credentials" "$manifest_path"; then
            result="FAILED"
            details="Missing service account credentials parameter"
            ((FAILED++))
        else
            details="Service account credentials properly configured"
            ((PASSED++))
        fi
    fi

    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242376() {
    local check_id="V-242376"
    local title="The Kubernetes Controller Manager must use TLS 1.2, at a minimum, to protect the confidentiality of sensitive data during electronic dissemination."
    local severity="MEDIUM"
    local result="PASSED"
    local details=""
    local manifest_path="/etc/kubernetes/manifests/kube-controller-manager.yaml"

    if [ ! -f "$manifest_path" ]; then
        result="FAILED"
        details="Controller Manager manifest missing"
        ((FAILED++))
    else
        tls_min_version=$(grep -oP -- "--tls-min-version=\K[^ ]+" "$manifest_path")
        if [[ -z "$tls_min_version" ]]; then
            result="FAILED"
            details="TLS minimum version not configured"
            ((FAILED++))
        elif [[ "$tls_min_version" == "VersionTLS12" || "$tls_min_version" == "VersionTLS13" ]]; then
            details="TLS version correctly configured: $tls_min_version"
            ((PASSED++))
        else
            result="FAILED"
            details="Unsupported TLS version: $tls_min_version"
            ((FAILED++))
        fi
    fi

    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242377() {
    local check_id="V-242377"
    local title="The Kubernetes Scheduler must use TLS 1.2, at a minimum, to protect the confidentiality of sensitive data during electronic dissemination."
    local severity="MEDIUM"
    local result="PASSED"
    local details=""
    local manifest_path="/etc/kubernetes/manifests/kube-scheduler.yaml"

    if [ ! -f "$manifest_path" ]; then
        result="FAILED"
        details="Scheduler manifest missing"
        ((FAILED++))
    else
        tls_min_version=$(grep -oP -- "--tls-min-version=\K[^ ]+" "$manifest_path")
        if [[ -z "$tls_min_version" ]]; then
            result="FAILED"
            details="TLS minimum version not configured"
            ((FAILED++))
        elif [[ "$tls_min_version" == "VersionTLS12" || "$tls_min_version" == "VersionTLS13" ]]; then
            details="TLS minimum version correctly set to $tls_min_version"
            ((PASSED++))
        else
            result="FAILED"
            details="Incorrect TLS version configured: $tls_min_version"
            ((FAILED++))
        fi
    fi

    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242378() {
    local check_id="V-242378"
    local title="The Kubernetes API Server must use TLS 1.2, at a minimum, to protect the confidentiality of sensitive data during electronic dissemination."
    local severity="MEDIUM"
    local result="PASSED"
    local details=""
    local manifest_path="/etc/kubernetes/manifests/kube-apiserver.yaml"

    if [ ! -f "$manifest_path" ]; then
        result="FAILED"
        details="API Server manifest missing"
        ((FAILED++))
    else
        tls_min_version=$(grep -oP -- "--tls-min-version=\K[^ ]+" "$manifest_path")
        if [[ -z "$tls_min_version" ]]; then
            result="FAILED"
            details="TLS minimum version not configured"
            ((FAILED++))
        elif [[ "$tls_min_version" == "VersionTLS12" || "$tls_min_version" == "VersionTLS13" ]]; then
            details="TLS minimum version correctly set to $tls_min_version"
            ((PASSED++))
        else
            result="FAILED"
            details="Incorrect TLS version configured: $tls_min_version"
            ((FAILED++))
        fi
    fi

    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242444() {
    local check_id="V-242444"
    local title="The Kubernetes component manifests must be owned by root."
    local severity="MEDIUM"
    local result="PASSED"
    local details=""
    local manifest_dir="/etc/kubernetes/manifests"
    local fail_count=0

    if [ ! -d "$manifest_dir" ]; then
        result="FAILED"
        details="Manifest directory missing"
        ((FAILED++))
    else
        while read -r file; do
            owner=$(stat -c "%U:%G" "$file")
            if [ "$owner" != "root:root" ]; then
                details+="Incorrect ownership: $file ($owner)<br>"
                fail_count=$((fail_count + 1))
            fi
        done < <(find "$manifest_dir" -type f 2>/dev/null)

        if [ $fail_count -gt 0 ]; then
            result="FAILED"
            details="Found $fail_count files with incorrect ownership:<br>$details"
            ((FAILED++))
        else
            details="All manifest files have correct ownership (root:root)"
            ((PASSED++))
        fi
    fi

    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242414() {
    local check_id="V-242414"
    local title="The Kubernetes cluster must use non-privileged host ports for user pods."
    local severity="MEDIUM"
    local result="PASSED"
    local details=""
    local system_ns=("kube-system" "kube-public" "kube-node-lease")
    local vulnerable_pods=()

    local all_pods
    all_pods=$(kubectl get pods --all-namespaces --no-headers 2>/dev/null | grep -vE "$(IFS="|"; echo "${system_ns[*]}")")

    if [[ -z "$all_pods" ]]; then
        details="No pods found in user namespaces."
        ((PASSED++))
    else
        while read -r line; do
            local namespace=$(awk '{print $1}' <<< "$line")
            local podname=$(awk '{print $2}' <<< "$line")
            local ports=$(kubectl get pod -n "$namespace" "$podname" -o jsonpath='{range .spec.containers[*].ports[*]}{.hostPort} {end}{range .spec.initContainers[*].ports[*]}{.hostPort} {end}' 2>/dev/null)

            local found=0
            for port in $ports; do
                if [[ $port =~ ^[0-9]+$ ]] && [ "$port" -le 1024 ] && [ "$port" -gt 0 ]; then
                    found=1
                    break
                fi
            done

            if [ $found -eq 1 ]; then
                vulnerable_pods+=("Namespace: $namespace | Pod: $podname | Ports: $ports")
            fi
        done <<< "$all_pods"

        if [ ${#vulnerable_pods[@]} -gt 0 ]; then
            result="FAILED"
            details="Found pods with privileged ports in user namespaces:<br>"
            for pod in "${vulnerable_pods[@]}"; do
                details+="- $pod<br>"
            done
            ((FAILED++))
        else
            details="No privileged ports detected in user namespaces."
            ((PASSED++))
        fi
    fi

    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v245544() {
    local check_id="V-245544"
    local title="Kubernetes endpoints must use approved organizational certificate and key pair to protect information in transit."
    local severity="HIGH"
    local result="PASSED"
    local details=""
    local manifest_path="/etc/kubernetes/manifests/kube-apiserver.yaml"
    local cert_configured=0
    local key_configured=0
    local cert_valid=0
    local key_valid=0

    if [ ! -f "$manifest_path" ]; then
        result="FAILED"
        details="API Server manifest missing."
        ((FAILED++))
    else
        local cert_line=$(grep -E -- "--kubelet-client-certificate(=[^[:space:]]+|[[:space:]]+[^[:space:]]+)" "$manifest_path")
        local key_line=$(grep -E -- "--kubelet-client-key(=[^[:space:]]+|[[:space:]]+[^[:space:]]+)" "$manifest_path")

        local cert_path=""
        if [[ -n "$cert_line" ]]; then
            cert_path=$(echo "$cert_line" | awk -F= '{print $2}' | awk '{print $1}')
            cert_configured=1
            if [ -f "$cert_path" ]; then
                cert_valid=1
            else
                details+="Certificate file not found: $cert_path<br>"
            fi
        else
            details+="--kubelet-client-certificate parameter not configured.<br>"
        fi

        local key_path=""
        if [[ -n "$key_line" ]]; then
            key_path=$(echo "$key_line" | awk -F= '{print $2}' | awk '{print $1}')
            key_configured=1
            if [ -f "$key_path" ]; then
                local perms=$(stat -c %a "$key_path")
                if [ "$perms" -ne 600 ]; then
                    details+="Key file has incorrect permissions: $key_path ($perms)<br>"
                else
                    key_valid=1
                fi
            else
                details+="Key file not found: $key_path<br>"
            fi
        else
            details+="--kubelet-client-key parameter not configured.<br>"
        fi

        if [[ $cert_configured -eq 1 && $key_configured -eq 1 && $cert_valid -eq 1 && $key_valid -eq 1 ]]; then
            details="Secure kubelet client authentication configured. Cert: $cert_path, Key: $key_path"
            ((PASSED++))
        else
            result="FAILED"
            details+="Missing or invalid client certificate/key configuration."
            ((FAILED++))
        fi
    fi

    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v254801() {
    local check_id="V-254801"
    local title="Kubernetes must enable PodSecurity admission controller on static pods and Kubelets."
    local severity="HIGH"
    local result="PASSED"
    local details=""
    local manifests_dir="/etc/kubernetes/manifests"
    local kubelet_config="/var/lib/kubelet/config.yaml"
    local failed=0

    # Control Plane Components Check
    if [[ -d "$manifests_dir" ]]; then
        find "$manifests_dir" -name "*.yaml" | while read manifest; do
            local fg_line=$(grep -iE -- "--feature-gates(=[^[:space:]]+| +[^[:space:]]+)" "$manifest")

            if [[ -z "$fg_line" ]]; then
                details+="Control plane component missing --feature-gates parameter: $manifest<br>"
                failed=1
            else
                local podsecurity_enabled=$(echo "$fg_line" | awk -F'[=,]' '{
                    for(i=1; i<=NF; i++) {
                        if($i ~ /PodSecurity/) {
                            split($i, arr, "=")
                            print (arr[2] == "true") ? 1 : 0
                            exit
                        }
                    }
                    print 0
                }')

                if [[ "$podsecurity_enabled" -eq 0 ]]; then
                    details+="PodSecurity disabled or not set to 'true': $manifest<br>"
                    failed=1
                fi
            fi
        done
    else
        result="FAILED"
        details="Control plane manifests directory missing: $manifests_dir"
        ((FAILED++))
    fi

    # Kubelet Configuration Check
    if [[ -f "$kubelet_config" ]]; then
        local podsecurity_config=$(yq '.featureGates.PodSecurity' "$kubelet_config" 2>/dev/null)
        if [[ "$podsecurity_config" != "true" ]]; then
            details+="PodSecurity feature gate not enabled in kubelet config: $kubelet_config<br>"
            failed=1
        fi
    else
        details+="Kubelet config file missing: $kubelet_config<br>"
        failed=1
    fi

    # Result Aggregation
    if [[ $failed -gt 0 ]]; then
        result="FAILED"
        details="Detected $failed violations:<br>$details"
        ((FAILED++))
    else
        details="All components comply with PodSecurity feature gate requirements"
        ((PASSED++))
    fi

    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v254800() {
    local check_id="V-254800"
    local title="Kubernetes must have a Pod Security Admission control file configured."
    local severity="HIGH"
    local result="PASSED"
    local details=""
    local admission_config_path=""

    # API Server Parameter Check
    local manifest_check=$(grep -Po -- "--admission-control-config-file(=\S+|\s+\S+)" /etc/kubernetes/manifests/kube-apiserver.yaml)

    if [[ -z "$manifest_check" ]]; then
        result="FAILED"
        details="API Server missing --admission-control-config-file parameter"
        ((FAILED++))
    else
        details="Pod Security Admission configuration compliant"
        ((PASSED++))
    fi

    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v245543() {
    local check_id="V-245543"
    local title="Kubernetes API Server must disable token authentication to protect information in transit."
    local severity="HIGH"
    local result="PASSED"
    local details=""
    local manifest_path="/etc/kubernetes/manifests/kube-apiserver.yaml"

    if [ ! -f "$manifest_path" ]; then
        result="FAILED"
        details="API Server manifest missing"
        ((FAILED++))
    else
        if grep -qE -- "--token-auth-file(=\S+|\s+\S+)" "$manifest_path"; then
            result="FAILED"
            details="Insecure static token authentication configured"
            ((FAILED++))
        else
            details="Secure authentication mechanism enforced"
            ((PASSED++))
        fi
    fi

    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v245542() {
    local check_id="V-245542"
    local title="Kubernetes API Server must disable basic authentication to protect information in transit."
    local severity="HIGH"
    local result="PASSED"
    local details=""
    local manifest_path="/etc/kubernetes/manifests/kube-apiserver.yaml"

    if [ ! -f "$manifest_path" ]; then
        result="FAILED"
        details="API Server manifest missing"
        ((FAILED++))
    else
        if grep -qE -- "--basic-auth-file(=\S+|\s+\S+)" "$manifest_path"; then
            result="FAILED"
            details="Insecure basic authentication configured"
            ((FAILED++))
        else
            details="Secure authentication mechanism enforced"
            ((PASSED++))
        fi
    fi

    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242434() {
    local check_id="V-242434"
    local title="Kubernetes Kubelet must enable kernel protection."
    local severity="HIGH"
    local result="PASSED"
    local details=""

#    # Check process arguments
#    local kubelet_cmd=$(ps -ef | grep kubelet | grep -v grep)
#    if echo "$kubelet_cmd" | grep -q -- "protect-kernel-defaults"; then
#        result="FAILED"
#        details="Dangerous process argument detected: --protect-kernel-defaults (should be configured via config file)"
#        ((FAILED++))
#    fi

    # Get config file path
    local config_file='/var/lib/kubelet/config.yaml'
    if grep "protectKernelDefaults" $config_file;then
       local protect_value=$(yq '.protectKernelDefaults' "$config_file" 2>/dev/null)
        if [[ "$protect_value" == "false" ]]; then
            result="FAILED"
            details="protectKernelDefaults not set to true )"
            ((FAILED++))
        fi
    else
        result="FAILED"
        details="protectKernelDefaults not set"
        ((FAILED++))
    fi

    if [[ $result == "PASSED" ]]; then
        details="Kernel protection properly configured"
        ((PASSED++))
    fi

    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242391() {
    local check_id="V-242391"
    local title="The Kubernetes Kubelet must have anonymous authentication disabled."
    local severity="HIGH"
    local result="PASSED"
    local details=""
    # Check process arguments
    local kubelet_cmd=$(ps -ef | grep kubelet | grep -v grep)
    local config_file=$(echo "$kubelet_cmd" | grep -oP -- '--config=\S+' | cut -d= -f2)
#    if echo "$kubelet_cmd" | grep -q -- "--anonymous-auth"; then
#        result="FAILED"
#        details="Dangerous process argument detected: --anonymous-auth"
#        ((FAILED++))
#    fi

    if [[ -f "$config_file" ]]; then
        local anonymous_enabled=$(yq '.authentication.anonymous.enabled' "$config_file" 2>/dev/null)
        if [[ "$anonymous_enabled" != "false" ]]; then
            result="FAILED"
            details+="${details:+<br>}Anonymous auth enabled in config (Current: ${anonymous_enabled:-undefined})"
            ((FAILED++))
        fi
    else
        result="FAILED"
        details+="${details:+<br>}Kubelet config file not found"
        ((FAILED++))
    fi

    if [[ "$result" == "PASSED" ]]; then
        details="Anonymous authentication properly disabled"
        ((PASSED++))
    fi

    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242390() {
    local check_id="V-242390"
    local title="The Kubernetes API server must have anonymous authentication disabled."
    local severity="HIGH"
    local result="PASSED"
    local details=""
    local manifest_file="/etc/kubernetes/manifests/kube-apiserver.yaml"

    if [[ ! -f "$manifest_file" ]]; then
        result="FAILED"
        details="API Server manifest missing"
        ((FAILED++))
    else
        local auth_line=$(grep -E -- "anonymous-auth(=[^[:space:]]+| +[^[:space:]]+)" "$manifest_file")
        if [[ -n "$auth_line" ]]; then
            local auth_value=$(echo "$auth_line" | awk -F'[= ]+' '{print $2}')
            if [[ "$auth_value" == "true" ]]; then
                result="FAILED"
                details="Anonymous authentication enabled (--anonymous-auth=true)"
                ((FAILED++))
            fi
        else
            result="FAILED"
            details="Anonymous auth parameter not explicitly configured"
            ((FAILED++))
        fi
    fi

    if [[ "$result" == "PASSED" ]]; then
        details="Anonymous authentication properly disabled"
        ((PASSED++))
    fi

    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242388() {
    local check_id="V-242388"
    local title="The Kubernetes API server must have the insecure bind address not set."
    local severity="HIGH"
    local result="PASSED"
    local details=""
    local manifest_path="/etc/kubernetes/manifests/kube-apiserver.yaml"

    if [[ -f "$manifest_path" ]]; then
        local insecure_bind=$(grep -E -- "--insecure-bind-address(=[^[:space:]]+| +[^[:space:]]+)" "$manifest_path")
        if [[ -n "$insecure_bind" ]]; then
            result="FAILED"
            details+="Insecure binding address configured: ${insecure_bind#*=}"
            ((FAILED++))
        fi
    else
        result="FAILED"
        details="API Server manifest missing"
        ((FAILED++))
    fi

    if [[ "$result" == "PASSED" ]]; then
        details="Secure binding configuration verified"
        ((PASSED++))
    fi

    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242387() {
    local check_id="V-242387"
    local title="The Kubernetes Kubelet must have the "readOnlyPort" flag disabled."
    local severity="HIGH"
    local result="PASSED"
    local details=""
    local config_file="/var/lib/kubelet/config.yaml"

    # Check process arguments
    local kubelet_cmd=$(ps -ef | grep kubelet | grep -v grep)
    if echo "$kubelet_cmd" | grep -q -- "--read-only-port"; then
        result="FAILED"
        details="Readonly port configured via process arguments"
        ((FAILED++))
    fi

    # Check config file
    if [[ -f "$config_file" ]]; then
        if grep 'readOnlyPort' $config_file;then
            local readonly_port=$(yq '.readOnlyPort' "$config_file" 2>/dev/null)
            if [[ "$readonly_port" != "0" ]]; then
                result="FAILED"
                details+="${details:+<br>}Readonly port enabled in config: ${readonly_port:-undefined}"
                ((FAILED++))
            fi
        fi
    else
        result="FAILED"
        details+="${details:+<br>}Kubelet config file missing"
        ((FAILED++))
    fi

    if [[ "$result" == "PASSED" ]]; then
        details="Readonly port properly disabled"
        ((PASSED++))
    fi

    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242386() {
    local check_id="V-242386"
    local title="API Server Insecure Port"
    local severity="HIGH"
    local result="PASSED"
    local details=""
    local manifest_file="/etc/kubernetes/manifests/kube-apiserver.yaml"

    if [[ -f "$manifest_file" ]]; then
        local insecure_port=$(grep -E -- "--secure-port(=[^[:space:]]+| +[^[:space:]]+)" "$manifest_file")
        if [[ -n "$insecure_port" ]]; then
            local port_value=$(echo "$insecure_port" | awk -F'[=]+' '{print $2}')
            if [[ "$port_value" != "6443" ]]; then
                result="FAILED"
                details="Insecure port enabled: $port_value"
                ((FAILED++))
            fi
        else
            result="FAILED"
            details="Insecure port not explicitly disabled"
            ((FAILED++))
        fi
    else
        result="FAILED"
        details="API Server manifest missing"
        ((FAILED++))
    fi

    if [[ "$result" == "PASSED" ]]; then
        details="Insecure port properly disabled"
        ((PASSED++))
    fi

    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

# 2025/05/27
check_v245541() {
    local check_id="V-245541"
    local title="Kubernetes Kubelet must not disable timeouts."
    local severity="MEDIUM"
    local result="PASSED"
    local details=""
    local config_file="/var/lib/kubelet/config.yaml"

    # Extract config file path from kubelet arguments (fallback to default)
    config_file=$(echo "$kubelet_cmd" | grep -oP -- '--config=\K[^ ]+' || echo "/var/lib/kubelet/config.yaml")

    # Step 2: Check config file for streamingConnectionIdleTimeout
    if [[ -f "$config_file" ]]; then
        local timeout=$(grep 'streamingConnectionIdleTimeout' "$config_file"|awk -F: '{print $2}' 2>/dev/null)
        if [[ -z "$timeout" ]]; then
            result="FAILED"
            details+="${details:+<br>}streamingConnectionIdleTimeout not configured in $config_file [2,4](@ref)"
            ((FAILED++))
        else
            # Convert timeout to minutes (supports s/m/h units)
            local minutes=$(echo $timeout|awk -F'm' '{print $1}')
            if [[ $timeout =~ ^([0-9]+)([a-z]*)$ ]]; then
                case "${BASH_REMATCH[2]}" in
                    "s") ((minutes = BASH_REMATCH[1] / 60)) ;;
                    "m") minutes=${BASH_REMATCH[1]} ;;
                    "h") ((minutes = BASH_REMATCH[1] * 60)) ;;
                    *)   minutes=0 ;; # Invalid unit
                esac
            fi

            if [[ $minutes -lt 5 ]]; then
                result="FAILED"
                details+="${details:+<br>}streamingConnectionIdleTimeout <5m in $config_file: $timeout [6,7](@ref)"
                ((FAILED++))
            fi
        fi
    else
        result="FAILED"
        details+="${details:+<br>}Kubelet config file missing: $config_file [9](@ref)"
        ((FAILED++))
    fi

    if [[ "$result" == "PASSED" ]]; then
        details="streaming-connection-idle-timeout properly configured (≥5m)"
        ((PASSED++))
    fi

    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242466() {
    local check_id="V-242466"
    local title="Kubernetes PKI CRT files must have permissions set to 644 or more restrictive."
    local severity="MEDIUM"
    local result="PASSED"
    local details=""
    local pki_dir="/etc/kubernetes/pki"

    # Check certificate files
    local crt_files=$(find $pki_dir -type f -name "*.crt" 2>/dev/null)
    if [[ -n "$crt_files" ]]; then
        while IFS= read -r file; do
            local perm=$(stat -c '%a' "$file")
            if [[ $perm -gt 644 ]]; then
                result="FAILED"
                details+="Excessive permissions for certificate: $file ($perm)<br>"
                ((FAILED++))
            fi
        done <<< "$crt_files"
    else
        result="FAILED"
        details+="No PKI certificate files found"
        ((FAILED++))
    fi

    if [[ "$result" == "PASSED" ]]; then
        details="All CRT files have compliant permissions"
        ((PASSED++))
    fi

    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242467() {
    local check_id="V-242467"
    local title="Kubernetes PKI key files must have permissions set to 600."
    local severity="MEDIUM"
    local result="PASSED"
    local details=""
    local pki_dir="/etc/kubernetes/pki"

    # Check private key files
    local key_files=$(find $pki_dir -type f -name "*.key" 2>/dev/null)
    if [[ -n "$key_files" ]]; then
        while IFS= read -r file; do
            local perm=$(stat -c '%a' "$file")
            if [[ $perm -gt 600 ]]; then
                result="FAILED"
                details+="Excessive permissions for private key: $file ($perm)<br>"
                ((FAILED++))
            fi
        done <<< "$key_files"
    else
        result="FAILED"
        details+="No PKI key files found"
        ((FAILED++))
    fi

    if [[ "$result" == "PASSED" ]]; then
        details="All KEY files have compliant permissions"
        ((PASSED++))
    fi

    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242465() {
    local check_id="V-242465"
    local title="Kubernetes API Server audit log path must be set"
    local severity="MEDIUM"
    local result="PASSED"
    local details=""
    local manifest_file="/etc/kubernetes/manifests/kube-apiserver.yaml"

    # Check audit log path configuration
    if [[ -f "$manifest_file" ]]; then
        local audit_path=$(grep -i "\-\-audit-log-path" "$manifest_file" | awk -F'=' '{print $2}')
        if [[ -z "$audit_path" || "$audit_path" == "" ]]; then
            result="FAILED"
            details="audit-log-path not configured"
            ((FAILED++))
        elif [[ ! -d $(dirname "$audit_path") ]]; then
            result="FAILED"
            details="audit log directory does not exist: $(dirname "$audit_path")"
            ((FAILED++))
        fi
    else
        result="FAILED"
        details="API Server manifest missing"
        ((FAILED++))
    fi

    if [[ "$result" == "PASSED" ]]; then
        details="audit-log-path properly configured"
        ((PASSED++))
    fi

    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242464() {
    local check_id="V-242464"
    local title="Kubernetes API Server audit log retention must be ≥30 days"
    local severity="MEDIUM"
    local result="PASSED"
    local details=""
    local manifest_file="/etc/kubernetes/manifests/kube-apiserver.yaml"

    # Check audit log max age
    if [[ -f "$manifest_file" ]]; then
        local max_age=$(grep -i "\-\-audit-log-maxage" "$manifest_file" | awk -F'=' '{print $2}' 2>/dev/null)
        if [[ -z "$max_age" ]]; then
            result="FAILED"
            details="audit-log-maxage not configured"
            ((FAILED++))
        elif [[ $max_age -lt 30 ]]; then
            result="FAILED"
            details="audit-log-maxage <30 days (current: $max_age)"
            ((FAILED++))
        fi
    else
        result="FAILED"
        details="API Server manifest missing"
        ((FAILED++))
    fi

    if [[ "$result" == "PASSED" ]]; then
        details="audit-log-maxage meets ≥30 day requirement"
        ((PASSED++))
    fi

    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242463() {
    local check_id="V-242463"
    local title="Kubernetes API Server audit log max backups must be ≥10"
    local severity="MEDIUM"
    local result="PASSED"
    local details=""
    local manifest_file="/etc/kubernetes/manifests/kube-apiserver.yaml"

    # Check audit log max backups
    if [[ -f "$manifest_file" ]]; then
        local max_backup=$(grep -i "\-\-audit-log-maxbackup" "$manifest_file" | awk -F'=' '{print $2}')
        if [[ -z "$max_backup" ]]; then
            result="FAILED"
            details="audit-log-maxbackup not configured"
            ((FAILED++))
        elif [[ $max_backup -lt 10 ]]; then
            result="FAILED"
            details="audit-log-maxbackup <10 (current: $max_backup)"
            ((FAILED++))
        fi
    else
        result="FAILED"
        details="API Server manifest missing"
        ((FAILED++))
    fi

    if [[ "$result" == "PASSED" ]]; then
        details="audit-log-maxbackup meets ≥10 requirement"
        ((PASSED++))
    fi

    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242462() {
    local check_id="V-242462"
    local title="Kubernetes API Server audit log max size must be ≥100MB"
    local severity="MEDIUM"
    local result="PASSED"
    local details=""
    local manifest_file="/etc/kubernetes/manifests/kube-apiserver.yaml"

    # Check audit log max size
    if [[ -f "$manifest_file" ]]; then
        local max_size=$(grep -i "\-\-audit-log-maxsize" "$manifest_file" | awk -F'=' '{print $2}' 2>/dev/null)
        if [[ -z "$max_size" ]]; then
            result="FAILED"
            details="audit-log-maxsize not configured"
            ((FAILED++))
        elif [[ $max_size -lt 100 ]]; then
            result="FAILED"
            details="audit-log-maxsize <100 MB (current: $max_size)"
            ((FAILED++))
        fi
    else
        result="FAILED"
        details="API Server manifest missing"
        ((FAILED++))
    fi

    if [[ "$result" == "PASSED" ]]; then
        details="audit-log-maxsize meets ≥100 MB requirement"
        ((PASSED++))
    fi

    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242461() {
    local check_id="V-242461"
    local title="Kubernetes API Server audit policy must be enabled"
    local severity="MEDIUM"
    local result="PASSED"
    local details=""
    local manifest_file="/etc/kubernetes/manifests/kube-apiserver.yaml"

    # Check audit policy configuration
    if [[ -f "$manifest_file" ]]; then
        local policy_file=$(grep -i "\-\-audit-policy-file" "$manifest_file" | awk -F'=' '{print $2}' 2>/dev/null)
        if [[ -z "$policy_file" ]]; then
            result="FAILED"
            details="audit-policy-file not configured"
            ((FAILED++))
        elif [[ ! -f "$policy_file" ]]; then
            result="FAILED"
            details="audit policy file missing: $policy_file"
            ((FAILED++))
        elif ! grep -q "apiVersion: audit.k8s.io/v1" "$policy_file" 2>/dev/null; then
            result="FAILED"
            details="invalid audit policy format"
            ((FAILED++))
        fi
    else
        result="FAILED"
        details="API Server manifest missing"
        ((FAILED++))
    fi

    if [[ "$result" == "PASSED" ]]; then
        details="audit-policy-file properly configured"
        ((PASSED++))
    fi

    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242460() {
    local check_id="V-242460"
    local title="Kubernetes admin kubeconfig files must have permissions ≤644"
    local severity="MEDIUM"
    local result="PASSED"
    local details=""
    local critical_files=(
        "/etc/kubernetes/admin.conf"
        "/etc/kubernetes/scheduler.conf"
        "/etc/kubernetes/controller-manager.conf"
    )

    for file in "${critical_files[@]}"; do
        if [[ -f "$file" ]]; then
            local perm=$(stat -c '%a' "$file")
            if [[ $perm -gt 644 ]]; then
                result="FAILED"
                details+="Excessive permissions for $file ($perm)<br>"
                ((FAILED++))
            fi
        else
            result="FAILED"
            details+="Missing critical file: $file<br>"
            ((FAILED++))
        fi
    done

    [[ "$result" == "PASSED" ]] && details="All kubeconfig files have compliant permissions" && ((PASSED++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242459() {
    local check_id="V-242459"
    local title="Kubernetes etcd files must have permissions ≤644"
    local severity="MEDIUM"
    local result="PASSED"
    local details=""
    local etcd_dir="/var/lib/etcd"

    if [[ -d "$etcd_dir" ]]; then
        while IFS= read -r file; do
            local perm=$(stat -c '%a' "$file")
            if [[ $perm -gt 644 ]]; then
                result="FAILED"
                details+="Excessive etcd permissions: $file ($perm)<br>"
                ((FAILED++))
            fi
        done < <(find "$etcd_dir" -type f)
    else
        result="FAILED"
        details+="Etcd directory missing"
        ((FAILED++))
    fi

    [[ "$result" == "PASSED" ]] && details="All etcd files have compliant permissions" && ((PASSED++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242457() {
    local check_id="V-242457"
    local title="Kubernetes kubelet config must be root-owned"
    local severity="MEDIUM"
    local result="PASSED"
    local details=""
    local kubelet_config="/var/lib/kubelet/config.yaml"

    if [[ -f "$kubelet_config" ]]; then
        local ownership=$(stat -c '%U:%G' "$kubelet_config")
        if [[ "$ownership" != "root:root" ]]; then
            result="FAILED"
            details+="Invalid ownership: $kubelet_config ($ownership)<br>"
            ((FAILED++))
        fi
    else
        result="FAILED"
        details+="Kubelet config missing"
        ((FAILED++))
    fi

    [[ "$result" == "PASSED" ]] && details="Kubelet config has proper ownership" && ((PASSED++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242456() {
    local check_id="V-242456"
    local title="Kubernetes kubelet config must have permissions ≤644"
    local severity="MEDIUM"
    local result="PASSED"
    local details=""
    local kubelet_config="/var/lib/kubelet/config.yaml"

    if [[ -f "$kubelet_config" ]]; then
        local perm=$(stat -c '%a' "$kubelet_config")
        if [[ $perm -gt 644 ]]; then
            result="FAILED"
            details+="Excessive kubelet permissions: $perm<br>"
            ((FAILED++))
        fi
    else
        result="FAILED"
        details+="Kubelet config missing"
        ((FAILED++))
    fi

    [[ "$result" == "PASSED" ]] && details="Kubelet config has compliant permissions" && ((PASSED++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242455() {
    local check_id="V-242455"
    local title="Kubernetes kubeadm.conf must have permissions ≤644"
    local severity="MEDIUM"
    local result="PASSED"
    local details=""
    local kubeadm_conf="/usr/lib/systemd/system/kubelet.service.d/10-kubeadm.conf"

    if [[ -f "$kubeadm_conf" ]]; then
        local perm=$(stat -c '%a' "$kubeadm_conf")
        if [[ $perm -gt 644 ]]; then
            result="FAILED"
            details+="Excessive kubeadm.conf permissions: $perm<br>"
            ((FAILED++))
        fi
    else
        result="FAILED"
        details+="Kubeadm config missing"
        ((FAILED++))
    fi

    [[ "$result" == "PASSED" ]] && details="Kubeadm.conf has compliant permissions" && ((PASSED++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242454() {
    local check_id="V-242454"
    local title="Kubernetes kubeadm.conf must be root-owned"
    local severity="MEDIUM"
    local result="PASSED"
    local details=""
    local kubeadm_conf="/usr/lib/systemd/system/kubelet.service.d/10-kubeadm.conf"

    if [[ -f "$kubeadm_conf" ]]; then
        local ownership=$(stat -c '%U:%G' "$kubeadm_conf")
        if [[ "$ownership" != "root:root" ]]; then
            result="FAILED"
            details="Invalid ownership: $kubeadm_conf ($ownership)"
            ((FAILED++))
        fi
    else
        result="FAILED"
        details="Kubeadm config missing"
        ((FAILED++))
    fi

    [[ "$result" == "PASSED" ]] && details="Kubeadm.conf has proper ownership" && ((PASSED++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242453() {
    local check_id="V-242453"
    local title="Kubernetes kubelet KubeConfig must be root-owned"
    local severity="MEDIUM"
    local result="PASSED"
    local details=""
    local kubelet_conf="/etc/kubernetes/kubelet.conf"

    if [[ -f "$kubelet_conf" ]]; then
        local ownership=$(stat -c '%U:%G' "$kubelet_conf")
        if [[ "$ownership" != "root:root" ]]; then
            result="FAILED"
            details="Invalid ownership: $kubelet_conf ($ownership)"
            ((FAILED++))
        fi
    else
        result="FAILED"
        details="Kubelet config missing"
        ((FAILED++))
    fi

    [[ "$result" == "PASSED" ]] && details="Kubelet.conf has proper ownership" && ((PASSED++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242452() {
    local check_id="V-242452"
    local title="Kubernetes kubelet KubeConfig permissions must ≤644"
    local severity="MEDIUM"
    local result="PASSED"
    local details=""
    local kubelet_conf="/etc/kubernetes/kubelet.conf"

    if [[ -f "$kubelet_conf" ]]; then
        local perm=$(stat -c '%a' "$kubelet_conf")
        if [[ $perm -gt 644 ]]; then
            result="FAILED"
            details="Excessive permissions: $kubelet_conf ($perm)"
            ((FAILED++))
        fi
    else
        result="FAILED"
        details="Kubelet config missing"
        ((FAILED++))
    fi

    [[ "$result" == "PASSED" ]] && details="Kubelet.conf has compliant permissions" && ((PASSED++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242451() {
    local check_id="V-242451"
    local title="Kubernetes PKI files must be root-owned"
    local severity="MEDIUM"
    local result="PASSED"
    local details=""
    local pki_dir="/etc/kubernetes/pki"

    if [[ -d "$pki_dir" ]]; then
        while IFS= read -r file; do
            local ownership=$(stat -c '%U:%G' "$file")
            if [[ "$ownership" != "root:root" ]]; then
                result="FAILED"
                details+="Invalid PKI ownership: $file ($ownership)<br>"
                ((FAILED++))
            fi
        done < <(find "$pki_dir" -type f)
    else
        result="FAILED"
        details+="PKI directory missing"
        ((FAILED++))
    fi

    [[ "$result" == "PASSED" ]] && details="All PKI files have proper ownership" && ((PASSED++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242450() {
    local check_id="V-242450"
    local title="Kubernetes Kubelet CA must be root-owned"
    local severity="MEDIUM"
    local result="PASSED"
    local details=""
    local ca_file=""

    # Check process arguments first
    local kubelet_cmd=$(ps -ef | grep kubelet | grep -v grep)
#    if echo "$kubelet_cmd" | grep -q -- "--client-ca-file"; then
#        result="FAILED"
#        details="client-ca-file configured via process arguments"
#        ((FAILED++))
#    fi

    # Check config file
    local config_file="/var/lib/kubelet/config.yaml"
    if [[ -f "$config_file" ]]; then
        ca_file=$(yq '.authentication.x509.clientCAFile' "$config_file" 2>/dev/null)
        if [[ -n "$ca_file" && -f "$ca_file" ]]; then
            local ownership=$(stat -c '%U:%G' "$ca_file")
            if [[ "$ownership" != "root:root" ]]; then
                result="FAILED"
                details+="Invalid CA ownership: $ca_file ($ownership)"
                ((FAILED++))
            fi
        fi
    fi

    [[ "$result" == "PASSED" ]] && details="Kubelet CA has proper ownership" && ((PASSED++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242449() {
    local check_id="V-242449"
    local title="Kubernetes Kubelet CA permissions must ≤644"
    local severity="MEDIUM"
    local result="PASSED"
    local details=""
    local ca_file=""

    # Check process arguments
    local kubelet_cmd=$(ps -ef | grep kubelet | grep -v grep)
#    if echo "$kubelet_cmd" | grep -q -- "--client-ca-file"; then
#        result="FAILED"
#        details="client-ca-file configured via process arguments"
#        ((FAILED++))
#    fi

    # Check config file
    local config_file=$(echo "$kubelet_cmd" | grep -oP -- '--config=\K[^ ]+' || echo "/var/lib/kubelet/config.yaml")
    if [[ -f "$config_file" ]]; then
        ca_file=$(yq '.authentication.x509.clientCAFile' "$config_file" 2>/dev/null)
        if [[ -n "$ca_file" && -f "$ca_file" ]]; then
            local perm=$(stat -c '%a' "$ca_file")
            if [[ $perm -gt 644 ]]; then
                result="FAILED"
                details+="Excessive CA permissions: $ca_file ($perm)"
                ((FAILED++))
            fi
        fi
    fi

    [[ "$result" == "PASSED" ]] && details="Kubelet CA has compliant permissions" && ((PASSED++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242448() {
    local check_id="V-242448"
    local title="Kubernetes Kube-Proxy kubeconfig must be root-owned"
    local severity="MEDIUM"
    local result="PASSED"
    local details=""
    local pod_name=$(kubectl get pod -n kube-system |grep kube-proxy|awk '{print $1}')
    local kube_proxy_conf="/var/lib/kube-proxy/..data/config.conf"

    if kubectl exec -it -n kube-system $pod_name -- ls /var/lib/kube-proxy/..data/config.conf  2>/dev/null; then
        local ownership=$(kubectl exec -it -n kube-system $pod_name -- stat -c '%U:%G'  /var/lib/kube-proxy/..data/config.conf |tr -d '\r')
        echo $ownership
        if [[ "$ownership" != 'root:root' ]]; then
            result="FAILED"
            details="Invalid ownership: $kube_proxy_conf ($ownership)[9,12](@ref)"
            ((FAILED++))
        fi
    else
        result="FAILED"
        details="Kube-proxy config missing[6](@ref)"
        ((FAILED++))
    fi

    [[ "$result" == "PASSED" ]] && details="Kube-proxy config has proper ownership" && ((PASSED++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242447() {
    local check_id="V-242447"
    local title="Kubernetes Kube-Proxy kubeconfig permissions must ≤644"
    local severity="MEDIUM"
    local result="PASSED"
    local details=""
    kube_proxy_conf="/var/lib/kube-proxy/..data/config.conf"
    local pod_name=$(kubectl get pod -n kube-system |grep kube-proxy|awk '{print $1}')

    if kubectl exec -it -n kube-system $pod_name -- ls /var/lib/kube-proxy/..data/config.conf  2>/dev/null; then
        local perm=$(kubectl exec -it -n kube-system $pod_name -- stat -c '%a'  /var/lib/kube-proxy/..data/config.conf|tr -d '\r')
        if [[ $perm -gt 644 ]]; then
            result="FAILED"
            details="Excessive permissions: $kube_proxy_conf ($perm)[9,12](@ref)"
            ((FAILED++))
        fi
    else
        result="FAILED"
        details="Kube-proxy config missing[6](@ref)"
        ((FAILED++))
    fi

    [[ "$result" == "PASSED" ]] && details="Kube-proxy config has compliant permissions" && ((PASSED++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242446() {
    local check_id="V-242446"
    local title="Kubernetes control plane configs must be root-owned"
    local severity="MEDIUM"
    local result="PASSED"
    local details=""
    local critical_files=(
        "/etc/kubernetes/admin.conf"
        "/etc/kubernetes/scheduler.conf"
        "/etc/kubernetes/controller-manager.conf"
    )

    for file in "${critical_files[@]}"; do
        if [[ -f "$file" ]]; then
            local ownership=$(stat -c '%U:%G' "$file")
            if [[ "$ownership" != "root:root" ]]; then
                result="FAILED"
                details+="Invalid ownership: $file ($ownership)[9,12](@ref)"
                ((FAILED++))
            fi
        else
            result="FAILED"
            details+="Missing critical file: $file [9](@ref)"
            ((FAILED++))
        fi
    done

    [[ "$result" == "PASSED" ]] && details="All control plane configs have proper ownership" && ((PASSED++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

# 0528
check_v242445() {
    local check_id="V-242445"
    local title="Etcd data directory ownership verification"
    local severity="MEDIUM"
    local result="PASSED"
    local details=""
    local etcd_dir="/var/lib/etcd"

    if [[ -d "$etcd_dir" ]]; then
        while IFS= read -r file; do
            local ownership=$(stat -c '%U:%G' "$file" | tr -d '\r')
            if [[ "$ownership" != "etcd:etcd" ]]; then

                result="FAILED"
                details+="Invalid ownership: $file ($ownership)\n"

            fi
        done < <(find "$etcd_dir" -type f)
    else
        result="FAILED"
        details+="Etcd directory missing"
        ((FAILED++))
    fi

    if [[ "$result" == "PASSED" ]];then
      details="All etcd files have proper ownership"
      ((PASSED++))
    else
       ((FAILED++))
       details="Invalid ownership"
    fi
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242438() {
    local check_id="V-242438"
    local title="API Server request timeout validation"
    local severity="MEDIUM"
    local result="PASSED"
    local details=""
    local api_manifest="/etc/kubernetes/manifests/kube-apiserver.yaml"

    if [[ -f "$api_manifest" ]]; then
        local timeout_setting=$(grep "request-timeout" "$api_manifest"|awk -F'=' '{print $2}')
        if [[ -z "$timeout_setting" || "$timeout_setting" == "0" ]]; then
            result="FAILED"
            details="Missing or invalid request-timeout configuration"
            ((FAILED++))
        fi
    else
        result="FAILED"
        details="API Server manifest missing"
        ((FAILED++))
    fi

    [[ "$result" == "PASSED" ]] && details="Request timeout properly configured" && ((PASSED++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242433() {
    local check_id="V-242433"
    local title="Etcd must configure peer-key-file"
    local severity="MEDIUM"
    local result="PASSED"
    local details=""
    local manifest="/etc/kubernetes/manifests/etcd.yaml"
    local required_param="peer-key-file"

    if [[ -f "$manifest" ]]; then
        if ! grep -q "$required_param" "$manifest"; then
            result="FAILED"
            details="Missing $required_param configuration"
            ((FAILED++))
        else
            local key_path=$(grep -oP "$required_param=\K[^ ]+" "$manifest")
            [[ ! -f "$key_path" ]] && { result="FAILED"; details="Key file not found: $key_path"; ((FAILED++)); }
        fi
    else
        result="FAILED"
        details="Etcd manifest missing"
        ((FAILED++))
    fi

    [[ "$result" == "PASSED" ]] && details="Etcd peer-key-file properly configured" && ((PASSED++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242432() {
    local check_id="V-242432"
    local title="Etcd must configure peer-cert-file"
    local severity="MEDIUM"
    local result="PASSED"
    local manifest="/etc/kubernetes/manifests/etcd.yaml"
    local required_param="peer-cert-file"

    if [[ -f "$manifest" ]]; then
        if ! grep -q "$required_param" "$manifest"; then
            result="FAILED"
            details="Missing $required_param configuration"
            ((FAILED++))
        else
            local cert_path=$(grep -oP "$required_param=\K[^ ]+" "$manifest")
            [[ ! -f "$cert_path" ]] && { result="FAILED"; details="Certificate file not found: $cert_path"; ((FAILED++)); }
        fi
    else
        result="FAILED"
        details="Etcd manifest missing"
        ((FAILED++))
    fi

    [[ "$result" == "PASSED" ]] && details="Etcd peer-cert-file properly configured" && ((PASSED++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242428() {
    local check_id="V-242428"
    local title="Etcd must configure cert-file"
    local severity="MEDIUM"
    local result="PASSED"
    local manifest="/etc/kubernetes/manifests/etcd.yaml"
    local required_param="\-\-cert-file"

    if [[ -f "$manifest" ]]; then
        if ! grep -q "$required_param" "$manifest"; then
            result="FAILED"
            details="Missing $required_param configuration"
            ((FAILED++))
        else
            local cert_path=$(grep -oP "$required_param=\K[^ ]+" "$manifest")
            [[ ! -f "$cert_path" ]] && { result="FAILED"; details="Certificate file not found: $cert_path"; ((FAILED++)); }
        fi
    else
        result="FAILED"
        details="Etcd manifest missing"
        ((FAILED++))
    fi

    [[ "$result" == "PASSED" ]] && details="Etcd cert-file properly configured" && ((PASSED++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242427() {
    local check_id="V-242427"
    local title="Etcd must configure key-file"
    local severity="MEDIUM"
    local result="PASSED"
    local manifest="/etc/kubernetes/manifests/etcd.yaml"
    local required_param="\-\-key-file"

    if [[ -f "$manifest" ]]; then
        if ! grep -q "$required_param" "$manifest"; then
            result="FAILED"
            details="Missing $required_param configuration"
            ((FAILED++))
        else
            local key_path=$(grep -oP "$required_param=\K[^ ]+" "$manifest")
            [[ ! -f "$key_path" ]] && { result="FAILED"; details="Key file not found: $key_path"; ((FAILED++)); }
        fi
    else
        result="FAILED"
        details="Etcd manifest missing"
        ((FAILED++))
    fi

    [[ "$result" == "PASSED" ]] && details="Etcd key-file properly configured" && ((PASSED++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242426() {
    local check_id="V-242426"
    local title="Etcd must enable peer auth"
    local severity="MEDIUM"
    local result="PASSED"
    local manifest="/etc/kubernetes/manifests/etcd.yaml"
    local required_param="peer-client-cert-auth=true"

    if [[ -f "$manifest" ]]; then
        if ! grep -q "$required_param" "$manifest"; then
            result="FAILED"
            details="Missing peer-client-cert-auth configuration"
            ((FAILED++))
        fi
    else
        result="FAILED"
        details="Etcd manifest missing"
        ((FAILED++))
    fi

    [[ "$result" == "PASSED" ]] && details="Etcd peer auth properly configured" && ((PASSED++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242431() {
    local check_id="V-242431"
    local title="API must configure etcd-key"
    local severity="MEDIUM"
    local result="PASSED"
    local manifest="/etc/kubernetes/manifests/kube-apiserver.yaml"
    local required_param="etcd-keyfile"

    if [[ -f "$manifest" ]]; then
        if ! grep -q "$required_param" "$manifest"; then
            result="FAILED"
            details="Missing $required_param configuration"
            ((FAILED++))
        else
            local key_path=$(grep -oP "$required_param=\K[^ ]+" "$manifest")
            [[ ! -f "$key_path" ]] && { result="FAILED"; details="Key file not found: $key_path"; ((FAILED++)); }
        fi
    else
        result="FAILED"
        details="API Server manifest missing"
        ((FAILED++))
    fi

    [[ "$result" == "PASSED" ]] && details="API Server etcd-keyfile properly configured" && ((PASSED++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242430() {
    local check_id="V-242430"
    local title="API must configure etcd-cert"
    local severity="MEDIUM"
    local result="PASSED"
    local manifest="/etc/kubernetes/manifests/kube-apiserver.yaml"
    local required_param="etcd-certfile"

    if [[ -f "$manifest" ]]; then
        if ! grep -q "$required_param" "$manifest"; then
            result="FAILED"
            details="Missing $required_param configuration"
            ((FAILED++))
        else
            local cert_path=$(grep -oP "$required_param=\K[^ ]+" "$manifest")
            [[ ! -f "$cert_path" ]] && { result="FAILED"; details="Certificate file not found: $cert_path"; ((FAILED++)); }
        fi
    else
        result="FAILED"
        details="API Server manifest missing"
        ((FAILED++))
    fi

    [[ "$result" == "PASSED" ]] && details="API Server etcd-certfile properly configured" && ((PASSED++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242429() {
    local check_id="V-242429"
    local title="API must configure etcd-ca"
    local severity="MEDIUM"
    local result="PASSED"
    local manifest="/etc/kubernetes/manifests/kube-apiserver.yaml"
    local required_param="etcd-cafile"

    if [[ -f "$manifest" ]]; then
        if ! grep -q "$required_param" "$manifest"; then
            result="FAILED"
            details="Missing $required_param configuration"
            ((FAILED++))
        else
            local ca_path=$(grep -oP "$required_param=\K[^ ]+" "$manifest")
            [[ ! -f "$ca_path" ]] && { result="FAILED"; details="CA certificate file not found: $ca_path"; ((FAILED++)); }
        fi
    else
        result="FAILED"
        details="API Server manifest missing"
        ((FAILED++))
    fi

    [[ "$result" == "PASSED" ]] && details="API Server etcd-cafile properly configured" && ((PASSED++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

####
check_v242425() {
    local check_id="V-242425"
    local title="Kubernetes Kubelet must enable tlsCertFile for client authentication to secure service."
    local severity="MEDIUM"
    local result="PASSED"
    local details=""
    local kubelet_cmd=$(ps -ef | grep kubelet | grep -v grep)
    local config_file="/var/lib/kubelet/config.yaml"

    # Extract config file path from process arguments
    local config_arg=$(echo "$kubelet_cmd" | grep -o -- "--config=[^ ]*")
    [[ -n "$config_arg" ]] && config_file=$(echo "$config_arg" | cut -d= -f2)
    [[ -z "$config_arg" ]] && config_arg=$(echo "$kubelet_cmd" | grep -o -- "--config [^ ]*" | awk '{print $2}') && [[ -n "$config_arg" ]] && config_file="$config_arg"

    # Check process arguments for --tls-cert-file
    if echo "$kubelet_cmd" | grep "\-\-tls-cert-file" 2>/dev/null; then
        result="PASSED"
        details="--tls-cert-file found in process arguments"
    fi

    # Check config file for tlsCertFile
#    if [[ -f "$config_file" ]]; then
#        local tls_cert=$(yq eval '.tlsCertFile' "$config_file" 2>/dev/null)
#        if [[ -z "$tls_cert" ]]; then
#            result="FAILED"
#            details+="${details:+<br>}tlsCertFile not set in $config_file"
#        fi
#    else
#        result="FAILED"
#        details+="${details:+<br>}Config file $config_file missing"
#    fi

    [[ "$result" == "PASSED" ]] && details="tlsCertFile properly configured";((PASSED++)) || ((FAILED++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242424() {
    local check_id="V-242424"
    local title="Kubernetes Kubelet must enable tlsPrivateKeyFile for client authentication to secure service."
    local severity="MEDIUM"
    local result="PASSED"
    local details=""
    local kubelet_cmd=$(ps -ef | grep kubelet | grep -v grep)
    local config_file="/var/lib/kubelet/config.yaml"

    # Check process args for --tls-private-key-file
    if echo "$kubelet_cmd" | grep "\-\-tls-private-key-file" 2>/dev/null; then
        result="PASSED"
        details="--tls-private-key-file found in process arguments"
    fi

    # Check config file
#    if [[ -f "$config_file" ]]; then
#        local tls_key=$(yq eval '.tlsPrivateKeyFile' "$config_file" 2>/dev/null)
#        if [[ -z "$tls_key" ]]; then
#            result="FAILED"
#            details+="${details:+<br>}tlsPrivateKeyFile not set in $config_file"
#            ((FAILED++))
#        fi
#    else
#        result="FAILED"
#        details+="${details:+<br>}Config file $config_file missing"
#        ((FAILED++))
#    fi

    [[ "$result" == "PASSED" ]] && details="tlsPrivateKeyFile properly configured" && ((PASSED++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242423() {
    local check_id="V-242423"
    local title="Kubernetes etcd must enable client authentication to secure service."
    local severity="MEDIUM"
    local result="PASSED"
    local details=""
    local manifests_dir="/etc/kubernetes/manifests"
    local etcd_files=$(grep -lis 'name: etcd' "$manifests_dir"/*)

    [[ -z "$etcd_files" ]] && result="FAILED" && details="No etcd manifest found"

    for file in $etcd_files; do
        # Check client-cert-auth setting
        if ! grep -iq -- "\-\-client-cert-auth=true" "$file"; then
            result="FAILED"
            if grep -iq -- "\-\-client-cert-auth=false" "$file"; then
                details+="${details:+<br>}client-cert-auth disabled in $file"
            else
                details+="${details:+<br>}client-cert-auth not configured in $file"
            fi
            ((FAILED++))
        fi
    done

    [[ "$result" == "PASSED" ]] && details="client-cert-auth properly enabled" && ((PASSED++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242422() {
    local check_id="V-242422"
    local title="Kubernetes API Server must have a certificate for communication."
    local severity="MEDIUM"
    local result="PASSED"
    local details=""
    local manifests_dir="/etc/kubernetes/manifests"
    local api_files=$(grep -lis 'kube-apiserver' "$manifests_dir"/*)

    [[ -z "$api_files" ]] && result="FAILED" && details="No API Server manifest found"

    for file in $api_files; do
        # Check TLS cert settings
        local cert_line=$(grep -i -- "\-\-tls-cert-file" "$file")
        local key_line=$(grep -i -- "\-\-tls-private-key-file" "$file")

        if [[ -z "$cert_line" ]]; then
            result="FAILED"
            details+="${details:+<br>}tls-cert-file missing in $file"
            ((FAILED++))
        fi

        if [[ -z "$key_line" ]]; then
            result="FAILED"
            details+="${details:+<br>}tls-private-key-file missing in $file"
            ((FAILED++))
        fi
    done

    [[ "$result" == "PASSED" ]] && details="API Server certificates properly configured" && ((PASSED++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242421() {
    local check_id="V-242421"
    local title="Kubernetes Controller Manager must have the SSL Certificate Authority set."
    local severity="MEDIUM"
    local result="PASSED"
    local details=""
    local cm_files="/etc/kubernetes/manifests/kube-controller-manager.yaml "

    [[ -z "$cm_files" ]] && result="FAILED" && details="No Controller Manager manifest found"

    for file in $cm_files; do
        if ! grep -iq -- "--root-ca-file" "$file"; then
            result="FAILED"
            details+="${details:+<br>}root-ca-file not configured in $file"
            ((FAILED++))
        fi
    done

    [[ "$result" == "PASSED" ]] && details="Controller Manager CA properly configured" && ((PASSED++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242420() {
    local check_id="V-242420"
    local title="Kubernetes Kubelet must have the SSL Certificate Authority set."
    local severity="MEDIUM"
    local result="PASSED"
    local details=""
    local kubelet_cmd=$(ps -ef | grep kubelet | grep -v grep)
    local config_file="/var/lib/kubelet/config.yaml"

    # Extract config path from process args
    local config_arg=$(echo "$kubelet_cmd" | grep -o -- "--config=[^ ]*")
    [[ -n "$config_arg" ]] && config_file=$(echo "$config_arg" | cut -d= -f2)
    [[ -z "$config_arg" ]] && config_arg=$(echo "$kubelet_cmd" | grep -o -- "--config [^ ]*" | awk '{print $2}') && [[ -n "$config_arg" ]] && config_file="$config_arg"

    # Check for --client-ca-file in process
    if echo "$kubelet_cmd" | grep  "\-\-client-ca-file" 2>/dev/null ; then
        result="PASSED"
        details="--client-ca-file found in process arguments"
    fi

    # Check config file
    if [[ -f "$config_file" ]]; then
        local ca_file=$(yq '.authentication.x509.clientCAFile' "$config_file")
        if [[ -z "$ca_file" ]]; then
            result="FAILED"
            details+="${details:+<br>}clientCAFile not set in $config_file"
            ((FAILED++))
        fi
    else
        result="FAILED"
        details+="${details:+<br>}Config file $config_file missing"
        ((FAILED++))
    fi

    [[ "$result" == "PASSED" ]] && details="Client CA properly configured" && ((PASSED++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242419() {
    local check_id="V-242419"
    local title="Kubernetes API Server must have the SSL Certificate Authority set."
    local severity="MEDIUM"
    local result="PASSED"
    local details=""
    local manifests_dir="/etc/kubernetes/manifests"

    # Check API server manifests
    local api_files=$(grep -lis 'kube-apiserver' "$manifests_dir"/*)
    [[ -z "$api_files" ]] && result="FAILED" && details="No API Server manifest found"

    for file in $api_files; do
        if ! grep -iq -- "--client-ca-file" "$file"; then
            result="FAILED"

            details+="${details:+<br>}client-ca-file not configured in $file"
            ((FAILED++))
        else
            local ca_file=$(grep -i -- "--client-ca-file" "$file" | awk -F= '{print $2}')
            [[ -z "$ca_file" ]] && result="FAILED" && details+="${details:+<br>}Empty client-ca-file value in $file"
        fi
    done

    [[ "$result" == "PASSED" ]] && details="client-ca-file properly configured" && ((PASSED++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242418() {
    local check_id="V-242418"
    local title="The Kubernetes API server must use approved cipher suites."
    local severity="MEDIUM"
    local result="PASSED"
    local details=""
    local manifests_dir="/etc/kubernetes/manifests"
    local required_ciphers="TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384"

    local api_files=$(grep -lis 'kube-apiserver' "$manifests_dir"/*)
    [[ -z "$api_files" ]] && result="FAILED" && details="No API Server manifest found"

    for file in $api_files; do
        local cipher_line=$(grep -i -- "--tls-cipher-suites" "$file")
        if [[ -z "$cipher_line" ]]; then
            result="FAILED"
            details+="${details:+<br>}tls-cipher-suites missing in $file"
            ((FAILED++))
        else
            local current_ciphers=$(echo "$cipher_line" | awk -F= '{print $2}' | tr -d '\r')
            for cipher in ${required_ciphers//,/ }; do
                if [[ ! "$current_ciphers" =~ "$cipher" ]]; then
                    result="FAILED"
                    details+="${details:+<br>}Missing required cipher $cipher in $file"
                    ((FAILED++))
                fi
            done
        fi
    done

    [[ "$result" == "PASSED" ]] && details="Approved cipher suites configured" && ((PASSED++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242417() {
    local check_id="V-242417"
    local title="Kubernetes must separate user functionality."
    local severity="MEDIUM"
    local result="PASSED"
    local details=""
    local sys_namespaces=("kube-system" "kube-public" "kube-node-lease")

    # Get all pods in system namespaces
    for ns in "${sys_namespaces[@]}"; do
        local user_pods=$(kubectl get pods -n "$ns" -o jsonpath='{.items[*].metadata.name}' 2>/dev/null | grep -v -E '(kube-apiserver|kube-controller|kube-proxy|kube-scheduler|etcd|core-dns|calico)')
        if [[ -n "$user_pods" ]]; then
            result="FAILED"
            details+="${details:+<br>}User pods found in $ns: $user_pods"
            ((FAILED++))
        fi
    done

    [[ "$result" == "PASSED" ]] && details="No user pods in system namespaces" && ((PASSED++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}


check_v242413(){
    local check_id="V-242413"
    local title="The Kubernetes etcd must enforce ports, protocols, and services (PPS) that adhere to the Ports, Protocols, and Services Management Category Assurance List (PPSM CAL)."
    local severity="MEDIUM"
    local result="WARN"
    local details="Manual inspection items, Details can be found at: https://stigviewer.com/stigs/kubernetes/2024-08-22/finding/V-242413"
    ((WARN++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242412(){
    local check_id="V-242412"
    local title="The Kubernetes Controllers must enforce ports, protocols, and services (PPS) that adhere to the Ports, Protocols, and Services Management Category Assurance List (PPSM CAL)."
    local severity="MEDIUM"
    local result="WARN"
    local details="Manual inspection items, Details can be found at: https://stigviewer.com/stigs/kubernetes/2024-08-22/finding/V-242412"
    ((WARN++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242411(){
    local check_id="V-242411"
    local title="The Kubernetes Scheduler must enforce ports, protocols, and services (PPS) that adhere to the Ports, Protocols, and Services Management Category Assurance List (PPSM CAL)."
    local severity="MEDIUM"
    local result="WARN"
    local details="Manual inspection items, Details can be found at: https://stigviewer.com/stigs/kubernetes/2024-08-22/finding/V-242411"
    ((WARN++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242410(){
    local check_id="V-242410"
    local title="The Kubernetes API Server must enforce ports, protocols, and services (PPS) that adhere to the Ports, Protocols, and Services Management Category Assurance List (PPSM CAL)."
    local severity="MEDIUM"
    local result="WARN"
    local details="Manual inspection items, Details can be found at: https://stigviewer.com/stigs/kubernetes/2024-08-22/finding/V-242410"
    ((WARN++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242409() {
    local check_id="V-242409"
    local title="Kubernetes Controller Manager must disable profiling."
    local severity="MEDIUM"
    local result="PASSED"
    local details=""
    local manifests_dir="/etc/kubernetes/manifests"
    local cm_files=$(grep -lis 'kube-controller-manager' "$manifests_dir"/*)

    [[ -z "$cm_files" ]] && result="FAILED" && details="No Controller Manager manifest found"

    for file in $cm_files; do
        local profiling=$(grep -i -- "--profiling" "$file")
        if [[ -n "$profiling" ]]; then
            if echo "$profiling" | grep -q "=true"; then
                result="FAILED"
                details+="${details:+<br>}Profiling enabled in $file"
                ((FAILED++))
            fi
        else
            # Default is enabled if not set
            result="FAILED"
            details+="${details:+<br>}Profiling not explicitly disabled in $file"
            ((FAILED++))
        fi
    done

    [[ "$result" == "PASSED" ]] && details="Profiling properly disabled" && ((PASSED++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242408() {
    local check_id="V-242408"
    local title="The Kubernetes manifest files must have least privileges."
    local severity="MEDIUM"
    local result="PASSED"
    local details=""
    local manifests_dir="/etc/kubernetes/manifests"

    # Check manifest file permissions
    while IFS= read -r -d $'\0' file; do
        local perms=$(stat -c %a "$file")
        if [[ "$perms" -gt 644 ]]; then
            result="FAILED"
            details+="${details:+<br>}Invalid permissions $perms for $file"
            ((FAILED++))
        fi
    done < <(find "$manifests_dir" -type f -print0)

    [[ "$result" == "PASSED" ]] && details="All manifest files have correct permissions" && ((PASSED++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242407() {
    local check_id="V-242407"
    local title="The Kubernetes KubeletConfiguration files must have file permissions set to 644 or more restrictive."
    local severity="MEDIUM"
    local result="PASSED"
    local details=""

    # Get kubelet config path
    local kubelet_cmd=$(ps -ef | grep kubelet | grep -v grep)
    local config_file=$(echo "$kubelet_cmd" | grep -o -- "--config=[^ ]*" | cut -d= -f2)
    [[ -z "$config_file" ]] && config_file="/var/lib/kubelet/config.yaml"

    # Check file permissions
    if [[ -f "$config_file" ]]; then
        local perms=$(stat -c %a "$config_file")
        if [[ "$perms" -gt 644 ]]; then
            result="FAILED"
            details="Invalid permissions $perms for $config_file"
            ((FAILED++))
        fi
    else
        result="FAILED"
        details="Kubelet config file not found"
        ((FAILED++))
    fi

    [[ "$result" == "PASSED" ]] && details="Kubelet config has correct permissions" && ((PASSED++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

#
check_v242406() {
    local check_id="V-242406"
    local title="The Kubernetes KubeletConfiguration file must be owned by root."
    local severity="MEDIUM"
    local result="PASSED"
    local details=""

    # 动态获取kubelet配置文件路径
    local kubelet_cmd=$(ps -ef | grep kubelet | grep -v grep)
    local config_file=$(echo "$kubelet_cmd" | grep -o -- "--config=[^ ]*" | cut -d= -f2)
    [[ -z "$config_file" ]] && config_file="/var/lib/kubelet/config.yaml"

    if [[ -f "$config_file" ]]; then
        local owner=$(stat -c %U:%G "$config_file")
        if [[ "$owner" != "root:root" ]]; then
            result="FAILED"
            details="Invalid ownership $owner for $config_file"
            ((FAILED++))
        fi
    else
        result="FAILED"
        details="Kubelet config file not found"
        ((FAILED++))
    fi

    [[ "$result" == "PASSED" ]] && details="Kubelet config has correct ownership" && ((PASSED++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242405() {
    local check_id="V-242405"
    local title="The Kubernetes manifests must be owned by root."
    local severity="MEDIUM"
    local result="PASSED"
    local details=""
    local manifests_dir="/etc/kubernetes/manifests"

    while IFS= read -r -d $'\0' file; do
        local owner=$(stat -c %U:%G "$file")
        if [[ "$owner" != "root:root" ]]; then
            result="FAILED"
            details+="${details:+<br>}Invalid ownership $owner for $file"
            ((FAILED++))
        fi
    done < <(find "$manifests_dir" -type f -print0)

    [[ "$result" == "PASSED" ]] && details="All manifest files have correct ownership" && ((PASSED++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242404() {
    local check_id="V-242404"
    local title="Kubernetes Kubelet must deny hostname override."
    local severity="MEDIUM"
    local result="PASSED"
    local details=""
    local kubelet_cmd=$(ps -ef | grep kubelet | grep -v grep)

    if echo "$kubelet_cmd" | grep -q -- "--hostname-override"; then
        result="FAILED"
        details="--hostname-override found in kubelet arguments"
        ((FAILED++))
    fi

    [[ "$result" == "PASSED" ]] && details="Hostname override properly disabled" && ((PASSED++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242403() {
    local check_id="V-242403"
    local title="Kubernetes API Server must generate audit records."
    local severity="MEDIUM"
    local result="PASSED"
    local manifests_dir="/etc/kubernetes/manifests"
    local api_files=$(grep -lis 'kube-apiserver' "$manifests_dir"/*)

    [[ -z "$api_files" ]] && result="FAILED" && details="No API Server manifest found"

    for file in $api_files; do
        if ! grep -iq -- "--audit-policy-file" "$file"; then
            result="FAILED"
            details+="${details:+<br>}audit-policy-file not configured in $file"
            ((FAILED++))
        fi
    done

    [[ "$result" == "PASSED" ]] && details="Audit policy properly configured" && ((PASSED++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242402() {
    local check_id="V-242402"
    local title="The Kubernetes API Server must have an audit log path set."
    local severity="MEDIUM"
    local result="PASSED"
    local manifests_dir="/etc/kubernetes/manifests"
    local api_files=$(grep -lis 'kube-apiserver' "$manifests_dir"/*)

    for file in $api_files; do
        if ! grep -iq -- "--audit-log-path" "$file"; then
            result="FAILED"
            details+="${details:+<br>}audit-log-path missing in $file"
            ((FAILED++))
        fi
    done

    [[ "$result" == "PASSED" ]] && details="Audit log path properly configured" && ((PASSED++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242400() {
    local check_id="V-242400"
    local title="The Kubernetes API server must have Alpha APIs disabled."
    local severity="MEDIUM"
    local result="PASSED"
    local manifests_dir="/etc/kubernetes/manifests"
    local api_files=$(grep -lis 'kube-apiserver' "$manifests_dir"/*)

    for file in $api_files; do
        local feature_gates=$(grep -i -- "--feature-gates" "$file")
        if [[ "$feature_gates" =~ "AllAlpha=true" ]]; then
            result="FAILED"
            details+="${details:+<br>}Alpha APIs enabled via feature-gates in $file"
            ((FAILED++))
        fi
    done

    [[ "$result" == "PASSED" ]] && details="Alpha APIs properly disabled" && ((PASSED++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

###
check_v242398() {
    local check_id="V-242398"
    local title="Kubernetes must disable DynamicAuditing feature gate"
    local severity="MEDIUM"
    local result="PASSED"
    local details=""
    local manifests_dir="/etc/kubernetes/manifests"

    # 检查控制平面配置文件
    local found_issue=false
    grep -ir --include=*.yaml "feature-gates.*DynamicAuditing=true" "$manifests_dir" | grep -v "^#" && found_issue=true

    # 检查kubelet进程参数
    local kubelet_cmd=$(ps -ef | grep kubelet | grep -v grep)
    if echo "$kubelet_cmd" | grep -q -- "--feature-gates"; then
        result="FAILED"
        details+="Feature-gates parameter found in kubelet process arguments"
        ((FAILED++))
    fi

    # 检查kubelet配置文件
    local config_file=$(echo "$kubelet_cmd" | grep -o -- "--config=[^ ]*" | cut -d= -f2)
    [[ -z "$config_file" ]] && config_file="/var/lib/kubelet/config.yaml"

    if [[ -f "$config_file" ]]; then
        if yq '.featureGates.DynamicAuditing' "$config_file" 2>/dev/null | grep -q "true"; then
            result="FAILED"
            details+="${details:+<br>}DynamicAuditing enabled in config file"
            ((FAILED++))
        fi
    fi

    [[ "$result" == "PASSED" ]] && details="DynamicAuditing properly disabled" && ((PASSED++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242396() {
    local check_id="V-242396"
    local title="Kubernetes must use kubectl v1.12.9+"
    local severity="MEDIUM"
    local result="PASSED"
    local details=""
    local min_version="1.12.9"

    # 获取kubectl版本
    local version_output=$(kubectl version --client 2>&1 | grep "Client Version")
    local client_version=$(echo "$version_output" | awk -F'v' '{print $2}' | awk -F'.' '{print $1"."$2"."$3}')

    # 版本比较逻辑
    if [ "$(printf '%s\n' "$min_version" "$client_version" | sort -V | head -n1)" != "$min_version" ]; then
        result="FAILED"
        details="Outdated kubectl version: $client_version"
        ((FAILED++))
    fi

    [[ "$result" == "PASSED" ]] && details="kubectl version $client_version compliant" && ((PASSED++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242395() {
    local check_id="V-242395"
    local title="Kubernetes Dashboard must be disabled"
    local severity="MEDIUM"
    local result="PASSED"

    # 检查dashboard相关资源
    local dashboard_pods=$(kubectl get pods --all-namespaces -l k8s-app=kubernetes-dashboard 2>/dev/null)

    if [[ -n "$dashboard_pods" ]]; then
        result="FAILED"
        details="Active dashboard pods detected"
        ((FAILED++))
    fi

    [[ "$result" == "PASSED" ]] && details="No dashboard components found" && ((PASSED++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242394() {
    local check_id="V-242394"
    local title="Worker nodes must stop SSH service"
    local severity="MEDIUM"
    local result="PASSED"
    local details=""

    local ssh_active=$(systemctl is-active sshd 2>/dev/null)

    if [[ "$ssh_active" == "active" ]]; then
        result="FAILED"
        details+="${details:+<br>}SSHD active on $node"
        ((FAILED++))
    fi

    [[ "$result" == "PASSED" ]] && details="SSH service properly disabled" && ((PASSED++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242393() {
    local check_id="V-242393"
    local title="Worker nodes must disable SSH service"
    local severity="MEDIUM"
    local result="PASSED"
    local details=""


    # 检查服务状态
    local ssh_status=$(systemctl is-enabled sshd 2>/dev/null)

    if [[ "$ssh_status" == "enabled" ]]; then
        result="FAILED"
        details+="${details:+<br>}SSHD enabled on $node"
        ((FAILED++))
    fi


    [[ "$result" == "PASSED" ]] && details="SSH service properly disabled" && ((PASSED++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242389() {
    local check_id="V-242389"
    local title="The Kubernetes API server must have the secure port set."
    local severity="MEDIUM"
    local result="PASSED"
    local manifests_dir="/etc/kubernetes/manifests"
    local api_files=$(grep -lis 'kube-apiserver' "$manifests_dir"/*)

    [[ -z "$api_files" ]] && result="FAILED" && details="No API Server manifest found"

    for file in $api_files; do
        local secure_port=$(grep -i -- "--secure-port" "$file" | awk -F= '{print $2}')
        if [[ "$secure_port" -eq 0 ]] || [[ -z "$secure_port" ]]; then
            result="FAILED"
            details+="${details:+<br>}Invalid secure-port configuration in $file (must not be 0)"
            ((FAILED++))
        fi
    done

    [[ "$result" == "PASSED" ]] && details="Secure port properly configured" && ((PASSED++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242385() {
    local check_id="V-242385"
    local title="The Kubernetes Controller Manager must have secure binding."
    local severity="MEDIUM"
    local result="PASSED"
    local cm_file="/etc/kubernetes/manifests/kube-controller-manager.yaml"

    if [[ -f "$cm_file" ]]; then
        local bind_addr=$(grep -i -- "--bind-address" "$cm_file" | awk -F= '{print $2}')
        if [[ "$bind_addr" != "127.0.0.1" ]]; then
            result="FAILED"
            details="Invalid bind-address $bind_addr in Controller Manager"
            ((FAILED++))
        fi
    else
        result="FAILED"
        details="Controller Manager manifest not found"
        ((FAILED++))
    fi

    [[ "$result" == "PASSED" ]] && details="Controller Manager binding secure" && ((PASSED++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242384() {
    local check_id="V-242384"
    local title="The Kubernetes Scheduler must have secure binding."
    local severity="MEDIUM"
    local result="PASSED"
    local scheduler_file="/etc/kubernetes/manifests/kube-scheduler.yaml"

    if [[ -f "$scheduler_file" ]]; then
        local bind_addr=$(grep -i -- "--bind-address" "$scheduler_file" | awk -F= '{print $2}')
        if [[ "$bind_addr" != "127.0.0.1" ]]; then
            result="FAILED"
            details="Invalid bind-address $bind_addr in Scheduler"
            ((FAILED++))
        fi
    else
        result="FAILED"
        details="Scheduler manifest not found"
        ((FAILED++))
    fi

    [[ "$result" == "PASSED" ]] && details="Scheduler binding secure" && ((PASSED++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242383() {
    local check_id="V-242383"
    local title="User resources must be in dedicated namespaces."
    local severity="MEDIUM"
    local result="PASSED"
    local sys_resources=$( kubectl get all -n default 2>&1 | grep -vE 'service/kubernetes|No resources found|NAME')
    kubectl config set-context --current --namespace=default
    if [[ -n "$sys_resources" ]]; then
        result="FAILED"
        details="Non-system resources found in default namespace"
        ((FAILED++))
    fi

    [[ "$result" == "PASSED" ]] && details="Default namespace isolation maintained" && ((PASSED++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242382() {
    local check_id="V-242382"
    local title="API Server must enable Node,RBAC authorization."
    local severity="MEDIUM"
    local result="PASSED"
    local manifests_dir="/etc/kubernetes/manifests"
    local api_files=$(grep -lis 'kube-apiserver' "$manifests_dir"/*)

    for file in $api_files; do
        local auth_mode=$(grep -i -- "--authorization-mode" "$file")
        if [[ "$auth_mode" =~ "AlwaysAllow" ]] || [[ -z "$auth_mode" ]]; then
            result="FAILED"
            details+="${details:+<br>}Invalid authorization mode in $file"
            ((FAILED++))
        elif ! [[ "$auth_mode" =~ "Node,RBAC" ]]; then
            result="FAILED"
            details+="${details:+<br>}Missing Node/RBAC modes in $file"
            ((FAILED++))
        fi
    done

    [[ "$result" == "PASSED" ]] && details="Authorization modes properly configured" && ((PASSED++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242379() {
    local check_id="V-242379"
    local title="The Kubernetes etcd must use TLS to protect the confidentiality of sensitive data during electronic dissemination."
    local severity="MEDIUM"
    local result="PASSED"
    local etcd_file="/etc/kubernetes/manifests/etcd.yaml"

    if [[ -f "$etcd_file" ]]; then

        # 检查auto-tls
        local auto_tls=$(grep -i -- "--auto-tls" "$etcd_file")
        if [[ "$auto_tls" =~ "true" ]] || [[ -z "$auto_tls" ]]; then
            result="FAILED"
            details+="${details:+<br>}Invalid auto-tls configuration"
            ((FAILED++))
        fi
    else
        result="FAILED"
        details="etcd manifest not found"
        ((FAILED++))
    fi

    [[ "$result" == "PASSED" ]] && details="etcd TLS properly configured" && ((PASSED++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242380() {
    local check_id="V-242380"
    local title="The Kubernetes etcd must use TLS to protect the confidentiality of sensitive data during electronic dissemination."
    local severity="MEDIUM"
    local result="PASSED"
    local etcd_file="/etc/kubernetes/manifests/etcd.yaml"

    if [[ -f "$etcd_file" ]]; then
        # 检查peer-auto-tls
        local peer_tls=$(grep -i -- "--peer-auto-tls" "$etcd_file")
        if [[ "$peer_tls" =~ "true" ]] || [[ -z "$peer_tls" ]]; then
            result="FAILED"
            details+="Invalid peer-auto-tls configuration"
            ((FAILED++))
        fi
    else
        result="FAILED"
        details="etcd manifest not found"
        ((FAILED++))
    fi

    [[ "$result" == "PASSED" ]] && details="etcd TLS properly configured" && ((PASSED++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242443(){
    local check_id="V-242443"
    local title="Kubernetes must contain the latest updates as authorized by IAVMs, CTOs, DTMs, and STIGs."
    local severity="MEDIUM"
    local result="WARN"
    local details="Manual inspection items, reference is needed“ https://kubernetes.io/releases/version-skew-policy/#supported -Comparing 'versions' to Kubernetes: Is it within its lifecycle"
    ((WARN++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242442(){
    local check_id="V-242442"
    local title="Kubernetes must remove old components after updated versions have been installed."
    local severity="MEDIUM"
    local result="WARN"
    local details="Manual inspection items, Kubernetes must remove old components after updated versions have been installed"
    ((WARN++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242437(){
    local check_id="V-242437"
    local title="Kubernetes must have a pod security policy set."
    local severity="MEDIUM"
    local result="WARN"
    local details="Psp are now deprecated and will be removed from version 1.25."
    ((WARN++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242436(){
    local check_id="V-242436"
    local title="The Kubernetes API server must have the ValidatingAdmissionWebhook enabled."
    local severity="MEDIUM"
    local result="WARN"
    local details="This check is only applicable for Kubernetes versions 1.25 and older."
    ((WARN++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}

check_v242399(){
    local check_id="V-242399"
    local title="Kubernetes DynamicKubeletConfig must not be enabled"
    local severity="MEDIUM"
    local result="WARN"
    local details="This check is only applicable for Kubernetes versions 1.25 and older."
    ((WARN++))
    add_report_row "$check_id" "$title" "$severity" "$result" "$details"
}


finalize_report() {
    total_checks=$((PASSED + FAILED + WARN))
    compliance_score=$(( total_checks > 0 ? PASSED * 100 / total_checks : 100 ))

    cat >> $REPORT_FILE << EOF
        </tbody>
    </table>

    <div class="summary">
        <h2>Scan Summary</h2>
        <p>Total Checks: $total_checks</p>
        <p>Passed: <span class="passed">$PASSED</span></p>
        <p>Failed: <span class="failed">$FAILED</span></p>
        <p>Warn: <span class="warn">$WARN</span></p>
        <p>Compliance Score: $compliance_score%</p>
    </div>
</body>
</html>
EOF
}

node() {
    # Perform all checks
#    if_tools
    check_v242397
    check_v242394
    check_v242393
#    finalize_report
    echo "Compliance report generated: file://$(pwd)/$REPORT_FILE"
}

master() {
    # Perform all checks
    if_tools
    check_v254801
    check_v254800
    check_v245544
    check_v245543
    check_v245542
    check_v245541
    check_v242467
    check_v242466
    check_v242465
    check_v242464
    check_v242463
    check_v242462
    check_v242461
    check_v242460
    check_v242459
    check_v242457
    check_v242456
    check_v242455
    check_v242454
    check_v242453
    check_v242452
    check_v242451
    check_v242450
    check_v242449
    check_v242448
    check_v242447
    check_v242446
    check_v242445
    check_v242444
    check_v242443
    check_v242442
    check_v242438
    check_v242437
    check_v242436
    check_v242434
    check_v242433
    check_v242432
    check_v242431
    check_v242430
    check_v242429
    check_v242428
    check_v242427
    check_v242426
    check_v242425
    check_v242424
    check_v242423
    check_v242422
    check_v242421
    check_v242420
    check_v242419
    check_v242418
    check_v242417
    check_v242415
    check_v242414
    check_v242413
    check_v242412
    check_v242411
    check_v242410
    check_v242409
    check_v242408
    check_v242407
    check_v242406
    check_v242405
    check_v242404
    check_v242403
    check_v242402
    check_v242400
    check_v242399
    check_v242398
    check_v242396
    check_v242395
    check_v242392
    check_v242391
    check_v242390
    check_v242389
    check_v242388
    check_v242387
    check_v242386
    check_v242385
    check_v242384
    check_v242383
    check_v242382
    check_v242381
    check_v242380
    check_v242379
    check_v242378
    check_v242377
    check_v242376
    finalize_report
    echo "Compliance report generated: file://$(pwd)/$REPORT_FILE"
}

case $1 in
     master)
       master
       ;;
     node)
       node
       ;;
     *)
        echo "Usage: (master|node)"
      ;;
    esac