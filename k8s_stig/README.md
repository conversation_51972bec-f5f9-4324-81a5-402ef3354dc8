
## kube-bench
kube-bench is a tool that checks whether Kubernetes is deployed securely by running the checks documented in the [CIS Kubernetes Benchmark](https://www.cisecurity.org/benchmark/kubernetes/).

## Quick start
```bash
tar xf k8s_stig.tar.gz
cd k8s_stig
bash main.sh (master|node)
```

## Directory Structure
```cfgrlanguage
k8s_stig/
|___binary_tool        # jq/yq tools

```

### Please Note

1. kube-bench implements the [CIS Kubernetes Benchmark](https://www.cisecurity.org/benchmark/kubernetes/) as closely as possible. Please raise issues here if kube-bench is not correctly implementing the test as described in the Benchmark. To report issues in the Benchmark itself (for example, tests that you believe are inappropriate), please join the [CIS community](https://cisecurity.org).

1. There is not a one-to-one mapping between releases of Kubernetes and releases of the CIS benchmark. See [CIS Kubernetes Benchmark support](docs/platforms.md#cis-kubernetes-benchmark-support) to see which releases of Kubernetes are covered by different releases of the benchmark.


By default, kube-bench will determine the test set to run based on the Kubernetes version running on the machine.
- see the following documentation on [Running kube-bench](docs/running.md#running-kube-bench) for more details.
