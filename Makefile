# Image URL to use all building/pushing image targets
IMG ?= build-harbor.alauda.cn/test/compliance/compliance-operator:latest
SCANNER_IMG ?= build-harbor.alauda.cn/test/compliance/compliance-scanner:latest
NODE_SCANNER_IMG ?= build-harbor.alauda.cn/test/compliance/node-scanner:latest
UNIFIED_SCANNER_IMG ?= build-harbor.alauda.cn/test/compliance/unified-scanner:exit-code-fix6
OS_CONTENT_IMG ?= build-harbor.alauda.cn/test/compliance-acp/os-content:latest
OPENSCAP_SCANNER_IMG ?= build-harbor.alauda.cn/test/compliance-acp/openscap-scanner:latest
CONTENT_EXTRACTOR_IMG ?= build-harbor.alauda.cn/test/compliance-acp/content-extractor:latest

# Get the currently used golang install path (in GOPATH/bin, unless GOBIN is set)
ifeq (,$(shell go env GOBIN))
GOBIN=$(shell go env GOPATH)/bin
else
GOBIN=$(shell go env GOBIN)
endif

# Setting SHELL to bash allows bash commands to be executed by recipes.
# This is a requirement for 'setup-envtest.sh' in the test target.
# Options are set to exit when a recipe line exits non-zero or a piped command fails.
SHELL = /usr/bin/env bash -o pipefail
.SHELLFLAGS = -ec

##@ General

# The help target prints out all targets with their descriptions organized
# beneath their categories. The categories are represented by '##@' and the
# target descriptions by '##'. The awk commands is responsible for reading the
# entire set of makefiles included in this invocation, looking for lines of the
# file as xyz: ## something, and then pretty-format the target and help. Then,
# if there's a line with ##@ something, that gets pretty-printed as a category.
# More info on the usage of ANSI control characters for terminal formatting:
# https://en.wikipedia.org/wiki/ANSI_escape_code#SGR_parameters
# More info on the awk command:
# http://linuxcommand.org/lc3_adv_awk.php

.PHONY: help
help: ## Display this help.
	@awk 'BEGIN {FS = ":.*##"; printf "\nUsage:\n  make \033[36m<target>\033[0m\n"} /^[a-zA-Z_0-9-]+:.*?##/ { printf "  \033[36m%-15s\033[0m %s\n", $$1, $$2 } /^##@/ { printf "\n\033[1m%s\033[0m\n", substr($$0, 5) } ' $(MAKEFILE_LIST)

##@ Development

.PHONY: manifests
manifests: controller-gen ## Generate WebhookConfiguration, ClusterRole and CustomResourceDefinition objects.
	$(CONTROLLER_GEN) rbac:roleName=manager-role crd webhook paths="./..." output:crd:artifacts:config=config/crd/bases

.PHONY: generate
generate: controller-gen ## Generate code containing DeepCopy, DeepCopyInto, and DeepCopyObject method implementations.
	$(CONTROLLER_GEN) object:headerFile="hack/boilerplate.go.txt" paths="./..."

.PHONY: fmt
fmt: ## Run go fmt against code.
	go fmt ./...

.PHONY: vet
vet: ## Run go vet against code.
	go vet ./...

.PHONY: test
test: manifests generate fmt vet envtest ## Run tests.
	KUBEBUILDER_ASSETS="$(shell $(ENVTEST) use $(ENVTEST_K8S_VERSION) -p path)" go test ./... -coverprofile cover.out

##@ Build

.PHONY: build
build: generate fmt vet ## Build manager binary.
	go build -o bin/manager cmd/manager/main.go

.PHONY: build-report-viewer
build-report-viewer: ## Build report viewer binary.
	go build -o bin/report-viewer cmd/report-viewer/main.go

.PHONY: build-all
build-all: build build-report-viewer ## Build all binaries.

.PHONY: run
run: manifests generate fmt vet ## Run a controller from your host.
	go run ./cmd/manager/main.go

.PHONY: docker-build
docker-build: ## Build docker image with the manager.
	docker build -t ${IMG} .

.PHONY: docker-push
docker-push: ## Push docker image with the manager.
	docker push ${IMG}

.PHONY: docker-build-unified-scanner
docker-build-unified-scanner: ## Build unified scanner docker image.
	docker build -f images/unified-scanner/Dockerfile -t ${UNIFIED_SCANNER_IMG} .

.PHONY: docker-push-unified-scanner
docker-push-unified-scanner: ## Push unified scanner docker image.
	docker push ${UNIFIED_SCANNER_IMG}

.PHONY: docker-build-os-content
docker-build-os-content: ## Build OS content docker image.
	docker build -f images/content/Dockerfile -t ${OS_CONTENT_IMG} images/content/

.PHONY: docker-push-os-content
docker-push-os-content: ## Push OS content docker image.
	docker push ${OS_CONTENT_IMG}

.PHONY: docker-build-openscap-scanner
docker-build-openscap-scanner: ## Build OpenSCAP scanner docker image.
	docker build -f images/openscap-scanner/Dockerfile -t ${OPENSCAP_SCANNER_IMG} images/openscap-scanner/

.PHONY: docker-push-openscap-scanner
docker-push-openscap-scanner: ## Push OpenSCAP scanner docker image.
	docker push ${OPENSCAP_SCANNER_IMG}

.PHONY: docker-build-content-extractor
docker-build-content-extractor: ## Build content extractor docker image.
	docker build -f images/content-extractor/Dockerfile -t ${CONTENT_EXTRACTOR_IMG} images/content-extractor/

.PHONY: docker-push-content-extractor
docker-push-content-extractor: ## Push content extractor docker image.
	docker push ${CONTENT_EXTRACTOR_IMG}

.PHONY: docker-build-all
docker-build-all: docker-build docker-build-unified-scanner docker-build-os-content docker-build-openscap-scanner docker-build-content-extractor ## Build all docker images.

.PHONY: docker-push-all
docker-push-all: docker-push docker-push-unified-scanner docker-push-os-content docker-push-openscap-scanner docker-push-content-extractor ## Push all docker images.

##@ Deployment

ifndef ignore-not-found
  ignore-not-found = false
endif

.PHONY: install
install: manifests kustomize ## Install CRDs into the K8s cluster specified in ~/.kube/config.
	$(KUSTOMIZE) build config/crd | kubectl apply -f -

.PHONY: uninstall
uninstall: manifests kustomize ## Uninstall CRDs from the K8s cluster specified in ~/.kube/config. Call with ignore-not-found=true to ignore resource not found errors during deletion.
	$(KUSTOMIZE) build config/crd | kubectl delete --ignore-not-found=$(ignore-not-found) -f -

.PHONY: deploy
deploy: manifests kustomize ## Deploy controller to the K8s cluster specified in ~/.kube/config.
	cd config/manager && $(KUSTOMIZE) edit set image controller=${IMG}
	$(KUSTOMIZE) build config/default | kubectl apply -f -

.PHONY: undeploy
undeploy: ## Undeploy controller from the K8s cluster specified in ~/.kube/config. Call with ignore-not-found=true to ignore resource not found errors during deletion.
	$(KUSTOMIZE) build config/default | kubectl delete --ignore-not-found=$(ignore-not-found) -f -

.PHONY: helm-package
helm-package: ## Package helm chart.
	helm package charts/compliance-operator

.PHONY: helm-install
helm-install: ## Install using helm chart.
	helm upgrade --install compliance-operator charts/compliance-operator --namespace compliance-system --create-namespace

.PHONY: helm-uninstall
helm-uninstall: ## Uninstall using helm chart.
	helm uninstall compliance-operator --namespace compliance-system

##@ Build Dependencies

## Location to install dependencies to
LOCALBIN ?= $(shell pwd)/bin
$(LOCALBIN):
	mkdir -p $(LOCALBIN)

## Tool Binaries
KUSTOMIZE ?= $(LOCALBIN)/kustomize
CONTROLLER_GEN ?= $(LOCALBIN)/controller-gen
ENVTEST ?= $(LOCALBIN)/setup-envtest

## Tool Versions
KUSTOMIZE_VERSION ?= v3.8.7
CONTROLLER_TOOLS_VERSION ?= v0.14.0
SETUP_ENVTEST_VERSION ?= v0.14.0

KUSTOMIZE_INSTALL_SCRIPT ?= "https://raw.githubusercontent.com/kubernetes-sigs/kustomize/master/hack/install_kustomize.sh"
.PHONY: kustomize
kustomize: $(KUSTOMIZE) ## Download kustomize locally if necessary.
$(KUSTOMIZE): $(LOCALBIN)
	test -s $(LOCALBIN)/kustomize || { curl -s $(KUSTOMIZE_INSTALL_SCRIPT) | bash -s -- $(subst v,,$(KUSTOMIZE_VERSION)) $(LOCALBIN); }

.PHONY: controller-gen
controller-gen: $(CONTROLLER_GEN) ## Download controller-gen locally if necessary.
$(CONTROLLER_GEN): $(LOCALBIN)
	test -s $(LOCALBIN)/controller-gen || GOBIN=$(LOCALBIN) go install sigs.k8s.io/controller-tools/cmd/controller-gen@$(CONTROLLER_TOOLS_VERSION)

.PHONY: envtest
envtest: $(ENVTEST) ## Download envtest-setup locally if necessary.
$(ENVTEST): $(LOCALBIN)
	test -s $(LOCALBIN)/setup-envtest || GOBIN=$(LOCALBIN) go install sigs.k8s.io/controller-runtime/tools/setup-envtest@latest

.PHONY: clean
clean: ## Clean build artifacts.
	rm -rf bin/
	rm -rf $(LOCALBIN)

.PHONY: all
all: build docker-build-all ## Build everything.

##@ Report Tools

.PHONY: build-report-tools
build-report-tools: ## Build all report download tools.
	@echo "Building report download tools..."
	go build -o bin/report-downloader cmd/report-downloader/main.go
	go build -o bin/report-api-server cmd/report-api-server/main.go
	@echo "Report tools built successfully in bin/"

.PHONY: report-downloader
report-downloader: ## Build the CLI report downloader tool.
	go build -o bin/report-downloader cmd/report-downloader/main.go

.PHONY: report-api-server
report-api-server: ## Build the report API server.
	go build -o bin/report-api-server cmd/report-api-server/main.go

.PHONY: start-report-api
start-report-api: report-api-server ## Start the report API server.
	@echo "Starting report API server on http://localhost:8080"
	./bin/report-api-server -port 8080

.PHONY: test-report-tools
test-report-tools: build-report-tools ## Test the unified report download functionality.
	@echo "Testing unified report download functionality..."
	./scripts/test-unified-report-download.sh

.PHONY: download-report
download-report: report-downloader ## Download a report using the CLI tool (requires SCAN_NAME).
	@if [ -z "$(SCAN_NAME)" ]; then \
		echo "Usage: make download-report SCAN_NAME=<scan-name> [NAMESPACE=<namespace>] [OUTPUT=<output-file>]"; \
		echo "Example: make download-report SCAN_NAME=stig-k8s-v2r2-node-scan"; \
		exit 1; \
	fi
	./bin/report-downloader -scan $(SCAN_NAME) $(if $(NAMESPACE),-namespace $(NAMESPACE)) $(if $(OUTPUT),-output $(OUTPUT))

# 添加 ENVTEST_K8S_VERSION 变量定义
ENVTEST_K8S_VERSION ?= 1.28.0