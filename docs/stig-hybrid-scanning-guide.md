# STIG 混合检查扫描指南

## 🎯 **什么是混合检查？**

STIG 规则经常需要**多维度检查**：
- **API 检查**：通过 Kubernetes API 验证集群配置
- **配置文件检查**：检查主机上的配置文件内容和权限
- **进程检查**：验证关键进程的运行状态和参数

传统的 `platform` 和 `node` 扫描器无法满足这种复合需求，因此我们引入了 **`hybrid` 扫描器**。

## 🔧 **Hybrid Scanner 的特点**

### ✅ **能力组合**
- **Kubernetes API 访问**：可以查询集群状态、节点信息、Pod 状态等
- **主机文件系统访问**：可以读取配置文件、检查权限、验证内容
- **进程和网络检查**：可以检查进程状态、端口监听、系统服务

### 🛡️ **安全配置**
- **特权容器**：需要 `privileged: true` 来访问主机资源
- **主机挂载**：挂载主机根目录到 `/host`
- **网络访问**：`hostNetwork: true` 用于网络检查
- **ServiceAccount**：需要足够的 RBAC 权限访问 Kubernetes API

### 📍 **节点调度**
- **精确调度**：通过 `nodeName` 调度到特定节点
- **节点范围控制**：支持 `control-plane`、`worker`、`all` 范围
- **角色识别**：自动识别节点角色并进行匹配验证

## 📝 **编写混合检查规则**

### 基本结构

```yaml
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: your-hybrid-rule
spec:
  checkType: "hybrid"  # 关键：指定为混合检查
  nodeScope: "control-plane"  # 可选：all, control-plane, worker
  checkScript: |
    #!/bin/bash
    # 你的混合检查脚本
```

### 可用的便捷函数

在 `checkScript` 中，你可以使用以下预定义函数：

#### 文件系统检查函数
```bash
# 检查主机文件是否存在
check_host_file "/etc/kubernetes/kubelet.conf"

# 读取主机文件内容
CONFIG=$(read_host_file "/etc/kubernetes/kubelet.conf")

# 检查文件权限
PERMS=$(stat -c "%a" "${HOST_ROOT}/etc/kubernetes/kubelet.conf")
```

#### 进程检查函数
```bash
# 检查进程是否运行
check_host_process "kubelet"

# 获取进程详细信息
PROC_INFO=$(get_host_process_info "kubelet")

# 检查端口监听
check_host_port "10250"
```

#### Kubernetes API 检查函数
```bash
# 执行 kubectl 命令
k8s_check get nodes

# 检查节点标签
check_node_label "node-role.kubernetes.io/control-plane" ""

# 检查节点注解
check_node_annotation "kubeadm.alpha.kubernetes.io/cri-socket" "unix:///var/run/containerd/containerd.sock"
```

## 🚀 **实际应用示例**

### 示例1：kubelet 安全配置检查

这个规则检查 kubelet 的配置文件、进程状态和节点 API 状态：

```yaml
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-kubelet-security-check
spec:
  id: "V-242415"
  checkType: "hybrid"
  nodeScope: "all"
  checkScript: |
    #!/bin/bash
    echo "=== STIG Kubelet Security Check ==="
    
    # 1. 检查配置文件
    if check_host_file "/etc/kubernetes/kubelet/kubelet-config.json"; then
        echo "✓ Kubelet config exists"
        
        # 检查权限
        PERMS=$(stat -c "%a" "${HOST_ROOT}/etc/kubernetes/kubelet/kubelet-config.json")
        if [ "$PERMS" = "600" ]; then
            echo "✓ Config permissions correct"
        else
            echo "✗ Config permissions: $PERMS"
            exit 1
        fi
        
        # 检查配置内容
        CONFIG=$(read_host_file "/etc/kubernetes/kubelet/kubelet-config.json")
        if echo "$CONFIG" | grep -q '"readOnlyPort": 0'; then
            echo "✓ Read-only port disabled"
        else
            echo "✗ Read-only port not disabled"
            exit 1
        fi
    else
        echo "✗ Kubelet config not found"
        exit 1
    fi
    
    # 2. 检查进程状态
    if check_host_process "kubelet"; then
        echo "✓ Kubelet process running"
    else
        echo "✗ Kubelet process not running"
        exit 1
    fi
    
    # 3. 检查节点 API 状态
    NODE_STATUS=$(k8s_check get node "$NODE_NAME" -o jsonpath='{.status.conditions[?(@.type=="Ready")].status}')
    if [ "$NODE_STATUS" = "True" ]; then
        echo "✓ Node is Ready"
    else
        echo "✗ Node not Ready: $NODE_STATUS"
        exit 1
    fi
    
    echo "=== All checks passed ==="
```

### 示例2：API Server 和网络策略检查

这个规则检查 API Server 配置和集群网络策略：

```yaml
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-apiserver-netpol-check
spec:
  id: "V-242420"
  checkType: "hybrid"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    echo "=== STIG API Server + Network Policy Check ==="
    
    # 1. 检查 API Server 配置
    if check_host_file "/etc/kubernetes/manifests/kube-apiserver.yaml"; then
        MANIFEST=$(read_host_file "/etc/kubernetes/manifests/kube-apiserver.yaml")
        
        if echo "$MANIFEST" | grep -q -- "--anonymous-auth=false"; then
            echo "✓ Anonymous auth disabled"
        else
            echo "✗ Anonymous auth not disabled"
            exit 1
        fi
    else
        echo "✗ API Server manifest not found"
        exit 1
    fi
    
    # 2. 检查 API Server 进程
    if check_host_process "kube-apiserver"; then
        echo "✓ API Server running"
        
        if check_host_port "6443"; then
            echo "✓ API Server listening on 6443"
        else
            echo "✗ API Server not listening on 6443"
            exit 1
        fi
    else
        echo "✗ API Server not running"
        exit 1
    fi
    
    # 3. 检查网络策略
    NETPOL_COUNT=$(k8s_check get networkpolicies --all-namespaces --no-headers | wc -l)
    if [ "$NETPOL_COUNT" -gt 0 ]; then
        echo "✓ Found $NETPOL_COUNT Network Policies"
    else
        echo "✗ No Network Policies found"
        exit 1
    fi
    
    echo "=== All checks passed ==="
```

## 🔍 **调试和故障排除**

### 查看混合扫描日志

```bash
# 查看混合扫描 Pod
kubectl get pods -n compliance-system -l compliance-operator.alauda.io/scan-type=hybrid

# 查看特定规则的日志
kubectl logs -n compliance-system -l compliance-operator.alauda.io/rule=your-rule-name

# 查看特定节点的扫描日志
kubectl logs -n compliance-system -l compliance-operator.alauda.io/node=your-node-name
```

### 常见问题

1. **权限不足**
   ```bash
   # 检查 ServiceAccount 权限
   kubectl auth can-i --list --as=system:serviceaccount:compliance-system:compliance-scanner
   ```

2. **主机文件访问失败**
   ```bash
   # 检查 HostPath 挂载
   kubectl describe pod your-scanner-pod -n compliance-system
   ```

3. **API 连接失败**
   ```bash
   # 检查网络连接
   kubectl exec -it your-scanner-pod -n compliance-system -- kubectl cluster-info
   ```

### 测试混合检查

```bash
# 创建测试扫描
kubectl apply -f - <<EOF
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Scan
metadata:
  name: hybrid-test
  namespace: compliance-system
spec:
  profile: your-hybrid-profile
  scanType: "hybrid"
EOF

# 监控扫描进度
kubectl get scans hybrid-test -n compliance-system -w

# 查看结果
kubectl describe scan hybrid-test -n compliance-system
```

## 📊 **性能考虑**

### 资源配置

混合扫描器需要更多资源：

```yaml
resources:
  requests:
    cpu: "200m"
    memory: "256Mi"
  limits:
    cpu: "1000m"
    memory: "1Gi"
```

### 并发控制

- 每个节点每个规则创建一个 Job
- 使用 `TTLSecondsAfterFinished: 300` 自动清理
- 通过 `nodeSelector` 控制扫描范围

## 🎯 **最佳实践**

1. **规则设计**
   - 将复杂检查拆分为多个简单规则
   - 使用清晰的错误消息
   - 提供详细的检查输出

2. **性能优化**
   - 避免不必要的 API 调用
   - 缓存重复使用的数据
   - 合理设置资源限制

3. **安全考虑**
   - 最小化特权权限使用
   - 验证输入参数
   - 避免敏感信息泄露

混合扫描器为 STIG 复杂规则检查提供了完美的解决方案，让您能够在一个扫描器中完成多维度的合规性验证！
