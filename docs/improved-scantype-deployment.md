# 改进的 ScanType 实现部署指南

## 🎯 **概述**

这个改进方案解决了原有 ScanType 实现中的概念混淆问题，提供了真正的平台级和节点级扫描分离。

## 📦 **新增组件**

### 1. **Platform Scanner 镜像**
- **路径**: `images/platform-scanner/`
- **功能**: 专用于平台级合规检查，只通过 Kubernetes API 操作
- **特点**: 非特权容器，不挂载主机文件系统

### 2. **Node Scanner 镜像**
- **路径**: `images/node-scanner/`
- **功能**: 专用于节点级合规检查，支持主机文件系统访问
- **特点**: 特权容器，支持节点角色控制

### 3. **增强的控制器逻辑**
- **文件**: `pkg/controller/scan/initialization_improved.go`
- **功能**: 正确实现 Platform 和 Node 扫描的概念分离

## 🚀 **部署步骤**

### 第一步：构建新的扫描器镜像

```bash
# 设置镜像仓库和标签
export REGISTRY="registry.alauda.cn:60070/test/compliance"
export TAG="v1.0.0"

# 构建镜像
chmod +x scripts/build-scanners.sh
./scripts/build-scanners.sh

# 推送到仓库（可选）
PUSH_IMAGES=true ./scripts/build-scanners.sh
```

### 第二步：更新环境变量

在 compliance-operator 部署中添加新的环境变量：

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: compliance-operator
spec:
  template:
    spec:
      containers:
      - name: compliance-operator
        env:
        # 新增的扫描器镜像配置
        - name: PLATFORM_SCANNER_IMAGE
          value: "registry.alauda.cn:60070/test/compliance/platform-scanner:v1.0.0"
        - name: NODE_SCANNER_IMAGE
          value: "registry.alauda.cn:60070/test/compliance/node-scanner:v1.0.0"
        
        # 保持现有配置
        - name: OPENSCAP_SCANNER_IMAGE
          value: "registry.alauda.cn:60070/test/compliance/openscap-scanner:latest"
        - name: CONTENT_IMAGE
          value: "registry.alauda.cn:60070/test/compliance/content:latest"
```

### 第三步：更新 RBAC 权限

Platform Scanner 需要额外的 Kubernetes API 权限：

```yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: compliance-scanner
rules:
# 现有权限...

# Platform Scanner 需要的额外权限
- apiGroups: [""]
  resources: ["nodes", "pods", "services", "endpoints", "namespaces"]
  verbs: ["get", "list"]
- apiGroups: ["rbac.authorization.k8s.io"]
  resources: ["roles", "rolebindings", "clusterroles", "clusterrolebindings"]
  verbs: ["get", "list"]
- apiGroups: ["networking.k8s.io"]
  resources: ["networkpolicies"]
  verbs: ["get", "list"]
- apiGroups: ["policy"]
  resources: ["podsecuritypolicies"]
  verbs: ["get", "list"]
- apiGroups: ["authorization.k8s.io"]
  resources: ["selfsubjectaccessreviews", "selfsubjectrulesreviews"]
  verbs: ["create"]
```

### 第四步：应用新的控制器逻辑

```bash
# 备份现有实现
cp pkg/controller/scan/initialization.go pkg/controller/scan/initialization_backup.go

# 应用改进的实现（需要手动集成关键部分）
# 主要是集成以下方法：
# - createImprovedPlatformScanJobs
# - createImprovedNodeScanJobs  
# - groupRulesByNodeScope
# - filterNodesByScope
# - getNodeRole (更新版本)
```

### 第五步：更新 API 定义

在 `pkg/apis/compliance/v1alpha1/types.go` 中确保包含：

```go
// Rule 结构体中添加
type RuleSpec struct {
    // 现有字段...
    
    // NodeScope specifies which types of nodes this rule applies to
    // +kubebuilder:validation:Enum=all;control-plane;worker;master
    // +optional
    NodeScope string `json:"nodeScope,omitempty"`
    
    // NodeSelector provides additional node selection criteria
    // +optional
    NodeSelector map[string]string `json:"nodeSelector,omitempty"`
}

// Scan 结构体中添加
type ScanSpec struct {
    // 现有字段...
    
    // NodeScopeStrategy determines how to handle rules with different NodeScope
    // +kubebuilder:validation:Enum=auto;manual
    // +optional
    NodeScopeStrategy string `json:"nodeScopeStrategy,omitempty"`
}
```

## 🧪 **测试验证**

### 1. **测试平台级扫描**

```bash
# 应用示例规则
kubectl apply -f examples/improved-scantype-rules.yaml

# 创建只包含平台级规则的扫描
kubectl apply -f - <<EOF
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Scan
metadata:
  name: platform-only-test
  namespace: compliance-system
spec:
  profile: stig-k8s-platform-rules
  scanType: "platform"
EOF

# 检查 Job 创建情况
kubectl get jobs -n compliance-system -l compliance-operator.alauda.io/scan-type=platform

# 检查 Pod 是否使用了正确的镜像和配置
kubectl get pods -n compliance-system -l compliance-operator.alauda.io/scan-type=platform -o yaml
```

### 2. **测试节点级扫描**

```bash
# 创建节点级扫描
kubectl apply -f - <<EOF
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Scan
metadata:
  name: node-scope-test
  namespace: compliance-system
spec:
  profile: stig-k8s-node-rules
  scanType: "node"
  nodeScopeStrategy: "auto"
EOF

# 检查不同节点范围的 Job
kubectl get jobs -n compliance-system -l compliance-operator.alauda.io/scan-type=node
kubectl get jobs -n compliance-system -l compliance-operator.alauda.io/node-scope=control-plane
kubectl get jobs -n compliance-system -l compliance-operator.alauda.io/node-scope=worker
```

### 3. **验证安全性**

```bash
# 检查平台级扫描 Pod 的安全上下文
kubectl get pods -n compliance-system -l compliance-operator.alauda.io/scan-type=platform -o jsonpath='{.items[*].spec.securityContext}'

# 应该显示非特权配置：
# {"runAsNonRoot":true,"runAsUser":1000,"runAsGroup":1000,"fsGroup":1000}

# 检查节点级扫描 Pod 的安全上下文
kubectl get pods -n compliance-system -l compliance-operator.alauda.io/scan-type=node -o jsonpath='{.items[*].spec.containers[*].securityContext}'

# 应该显示特权配置：
# {"privileged":true}
```

## 📊 **监控和日志**

### 查看扫描结果

```bash
# 查看平台级扫描结果
kubectl logs -n compliance-system -l compliance-operator.alauda.io/scan-type=platform

# 查看节点级扫描结果
kubectl logs -n compliance-system -l compliance-operator.alauda.io/scan-type=node

# 查看特定节点范围的结果
kubectl logs -n compliance-system -l compliance-operator.alauda.io/node-scope=control-plane
```

### 检查扫描状态

```bash
# 查看扫描进度
kubectl get scans -n compliance-system

# 查看详细状态
kubectl describe scan improved-stig-scan -n compliance-system
```

## 🔧 **故障排除**

### 常见问题

1. **Platform Scanner 权限不足**
   - 检查 ServiceAccount 和 RBAC 配置
   - 确保 platform-scanner 有足够的 API 权限

2. **Node Scanner 无法访问主机文件系统**
   - 检查 HostPath 挂载配置
   - 确保容器有特权权限

3. **节点角色识别错误**
   - 检查节点标签是否正确
   - 验证 `getNodeRole` 函数逻辑

### 调试命令

```bash
# 检查镜像是否正确拉取
kubectl describe pods -n compliance-system -l compliance-operator.alauda.io/scan-type=platform

# 检查环境变量配置
kubectl get deployment compliance-operator -n compliance-system -o yaml | grep -A 10 env

# 检查 RBAC 权限
kubectl auth can-i --list --as=system:serviceaccount:compliance-system:compliance-scanner
```

## 🎯 **预期效果**

部署完成后，您将获得：

1. **概念清晰的扫描架构**：Platform 和 Node 扫描有明确区别
2. **增强的安全性**：Platform 扫描使用非特权容器
3. **精细的节点控制**：支持 Control Plane/Worker 节点区分
4. **STIG 兼容性**：完美支持 STIG 规则的节点范围要求
5. **向后兼容性**：保持与现有 OpenSCAP 扫描的兼容

这个改进方案解决了您提到的 ScanType 设计初衷与实现不一致的问题，提供了完整、可部署的解决方案。
