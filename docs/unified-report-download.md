# 统一报告下载功能

## 概述

为了解决当前报告获取方式不统一的问题，我们实现了一个统一的报告下载服务。现在用户可以通过 scan name 直接获取最新的报告 zip 包，无论是 shell script 扫描还是 OpenSCAP 扫描。

## 问题背景

之前的报告获取方式存在以下问题：

1. **Shell script 扫描**：报告存储在 ConfigMap 中，需要解析 ConfigMap 字段导出 HTML 文件
2. **OpenSCAP 扫描**：报告存储在 openscap-report-service pod 中，需要使用 kubectl cp 复制文件
3. **操作复杂**：用户需要根据扫描类型使用不同的方法获取报告

## 解决方案

### 核心组件

1. **ReportDownloadService** (`pkg/controller/scan/report_download.go`)
   - 统一的报告下载服务
   - 自动检测扫描类型（shell script vs OpenSCAP）
   - 生成包含所有报告文件的 zip 包

2. **CLI 工具** (`cmd/report-downloader/main.go`)
   - 命令行工具，直接通过 scan name 下载报告
   - 支持自定义输出文件名和命名空间

3. **HTTP API 服务器** (`cmd/report-api-server/main.go`)
   - 提供 REST API 接口
   - 支持列出扫描和下载报告
   - 包含 Web 界面

4. **便捷脚本** (`scripts/download-report.sh`)
   - 封装了 CLI 和 API 两种使用方式
   - 提供友好的命令行界面

## 使用方法

### 方法 1：CLI 工具

```bash
# 下载指定扫描的最新报告
go run cmd/report-downloader/main.go -scan stig-k8s-v2r2-node-scan

# 指定输出文件
go run cmd/report-downloader/main.go -scan stig-k8s-v2r2-node-scan -output my-report.zip

# 指定命名空间
go run cmd/report-downloader/main.go -scan my-scan -namespace my-namespace
```

### 方法 2：HTTP API 服务器

启动 API 服务器：
```bash
go run cmd/report-api-server/main.go -port 8080
```

使用 API：
```bash
# 列出所有扫描
curl http://localhost:8080/scans

# 下载报告
curl -O http://localhost:8080/download/stig-k8s-v2r2-node-scan

# 健康检查
curl http://localhost:8080/health
```

### 方法 3：便捷脚本

```bash
# 使用 CLI 方式下载
./scripts/download-report.sh stig-k8s-v2r2-node-scan

# 使用 API 方式下载
./scripts/download-report.sh -m api stig-k8s-v2r2-node-scan

# 列出可用的扫描
./scripts/download-report.sh -l

# 指定输出目录
./scripts/download-report.sh -o /tmp/reports stig-k8s-v2r2-node-scan
```

## 报告内容结构

下载的 zip 文件包含以下内容：

```
scan-report.zip
├── metadata.txt                    # 扫描元数据信息
├── openscap-reports/              # OpenSCAP 报告（如果适用）
│   ├── report-node1-timestamp.html
│   ├── report-node2-timestamp.html
│   └── ...
└── shell-script-reports/          # Shell script 报告（如果适用）
    ├── report.html                # 主报告文件
    ├── results/                   # 原始结果文件
    │   ├── result-configmap1.xml
    │   └── result-configmap2.xml
    └── html/                      # 节点级别 HTML 报告
        ├── node1-report.html
        └── node2-report.html
```

## API 接口文档

### GET /scans
列出所有可用的扫描

**响应示例：**
```json
{
  "scans": [
    {
      "name": "stig-k8s-v2r2-node-scan",
      "namespace": "compliance-system",
      "profile": "stig-k8s-v2r2-node",
      "phase": "Done",
      "result": "NON-COMPLIANT",
      "scanId": "scan-20250630-010000-abc123",
      "timestamp": "2025-06-30 01:00:00"
    }
  ]
}
```

### GET /download/{scanName}
下载指定扫描的最新报告 zip 文件

**参数：**
- `scanName`: 扫描名称

**响应：**
- Content-Type: `application/zip`
- Content-Disposition: `attachment; filename={scanName}-report.zip`

### GET /health
健康检查接口

**响应示例：**
```json
{
  "status": "healthy",
  "timestamp": "2025-06-30T01:00:00Z",
  "service": "compliance-report-api"
}
```

## 技术实现细节

### 扫描类型检测

系统通过检查 Profile 资源的 `DataStream` 字段来判断扫描类型：

- 如果 `profile.Spec.DataStream != nil` 且 `ContentFile != ""`：OpenSCAP 扫描
- 否则：Shell script 扫描

### OpenSCAP 报告处理

1. 调用 openscap-report-service 的 `/download/{scanID}` 接口
2. 获取原始 zip 文件
3. 解压并重新打包到统一的目录结构中

### Shell Script 报告处理

1. 从 ConfigMap 获取主报告 HTML 文件
2. 收集所有相关的结果 ConfigMap
3. 提取 base64 编码的 HTML 内容
4. 组织到统一的目录结构中

### 错误处理

- 扫描不存在：返回 404 错误
- 没有最新结果：返回相应错误信息
- openscap-report-service 不可用：返回服务错误
- ConfigMap 不存在：返回相应错误信息

## 部署建议

### 生产环境部署

1. **作为 Kubernetes Service 部署**：
   ```yaml
   apiVersion: apps/v1
   kind: Deployment
   metadata:
     name: compliance-report-api
   spec:
     replicas: 2
     selector:
       matchLabels:
         app: compliance-report-api
     template:
       metadata:
         labels:
           app: compliance-report-api
       spec:
         containers:
         - name: report-api
           image: compliance-operator:latest
           command: ["/manager", "report-api-server"]
           ports:
           - containerPort: 8080
   ```

2. **配置 RBAC 权限**：
   确保服务账户有读取 Scan、Profile、ConfigMap 的权限

3. **配置 Service 和 Ingress**：
   ```yaml
   apiVersion: v1
   kind: Service
   metadata:
     name: compliance-report-api
   spec:
     selector:
       app: compliance-report-api
     ports:
     - port: 80
       targetPort: 8080
   ```

## 优势

1. **统一接口**：无论扫描类型，都使用相同的方法获取报告
2. **自动化**：自动检测扫描类型和报告位置
3. **完整性**：包含所有相关的报告文件和元数据
4. **便捷性**：提供多种使用方式（CLI、API、脚本）
5. **标准化**：统一的 zip 文件格式和目录结构

## 向后兼容性

- 现有的报告获取方式仍然可用
- 不影响现有的扫描流程
- 可以与现有工具并行使用

## 快速开始

### 1. 构建工具

```bash
# 构建所有报告工具
make build-report-tools

# 或者单独构建
make report-downloader
make report-api-server
```

### 2. 下载报告

```bash
# 方法1：使用 Makefile 目标
make download-report SCAN_NAME=stig-k8s-v2r2-node-scan

# 方法2：直接使用 CLI 工具
./bin/report-downloader -scan stig-k8s-v2r2-node-scan

# 方法3：使用便捷脚本
./scripts/download-report.sh stig-k8s-v2r2-node-scan
```

### 3. 启动 API 服务器

```bash
# 使用 Makefile 目标
make start-report-api

# 或者直接运行
./bin/report-api-server -port 8080
```

### 4. 测试功能

```bash
# 运行完整测试套件
make test-report-tools

# 或者直接运行测试脚本
./scripts/test-unified-report-download.sh
```

## 故障排除

### 常见问题

1. **"no latest result found for scan"**
   - 确保扫描已经完成并有结果
   - 检查 scan 的 status.latestResult 字段

2. **"openscap-report-service returned status 404"**
   - 确保 openscap-report-service 正在运行
   - 检查 scanID 是否正确

3. **"failed to get report ConfigMap"**
   - 确保 shell script 扫描的报告 ConfigMap 存在
   - 检查 ConfigMap 的标签是否正确

4. **API 服务器无法访问**
   - 检查端口是否被占用
   - 确保有正确的 kubeconfig 配置

### 调试技巧

1. **查看扫描状态**：
   ```bash
   kubectl get scan <scan-name> -o yaml
   ```

2. **查看报告 ConfigMap**：
   ```bash
   kubectl get configmap -l compliance-operator.alauda.io/scan=<scan-name>
   ```

3. **检查 openscap-report-service**：
   ```bash
   kubectl get pods -l app=openscap-report-service
   curl http://openscap-report-service.compliance-system.svc.cluster.local:8080/health
   ```

4. **查看 API 服务器日志**：
   ```bash
   ./bin/report-api-server -port 8080 # 查看控制台输出
   ```
