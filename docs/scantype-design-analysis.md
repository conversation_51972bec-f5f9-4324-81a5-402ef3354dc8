# ScanType 设计分析与改进方案

## 🔍 **当前实现分析**

### ❌ **发现的问题**

#### 1. **Platform 扫描概念混淆**
- **当前实现**：Platform 扫描仍然挂载主机文件系统 (`/host/proc`, `/host/etc/kubernetes`)
- **正确概念**：Platform 扫描应该只通过 Kubernetes API 进行集群级配置检查
- **影响**：无法区分真正的平台级检查和节点级检查

#### 2. **OpenSCAP Platform 扫描错误**
- **当前实现**：OpenSCAP Platform 扫描也挂载了主机文件系统
- **问题**：这实际上是节点级扫描，不是平台级扫描
- **OpenSCAP 现实**：OpenSCAP 主要用于节点级操作系统合规检查，很少有真正的平台级规则

#### 3. **缺少节点角色区分**
- **当前实现**：所有节点级扫描都在所有节点上执行
- **STIG 需求**：需要区分 Control Plane 和 Worker 节点的不同检查

## 🎯 **正确的 ScanType 设计**

### ✅ **Platform 扫描 (集群级)**
```yaml
# 特征：
- 通过 Kubernetes API 检查集群配置
- 不需要访问主机文件系统
- 不需要特权容器
- 检查内容：RBAC、NetworkPolicy、PodSecurityPolicy 等

# 示例规则：
checkType: "platform"
checkScript: |
  #!/bin/bash
  # 检查是否启用了 RBAC
  kubectl auth can-i --list --as=system:anonymous
  # 检查 NetworkPolicy 配置
  kubectl get networkpolicies --all-namespaces
```

### ✅ **Node 扫描 (节点级)**
```yaml
# 特征：
- 需要访问主机文件系统
- 需要特权容器或特定权限
- 检查内容：文件权限、进程配置、系统设置等

# 示例规则：
checkType: "node"
nodeScope: "control-plane"  # 新增：节点角色控制
checkScript: |
  #!/bin/bash
  # 检查 admin.conf 文件权限（只在 Control Plane）
  stat -c "%a %U:%G" /host/etc/kubernetes/admin.conf
```

## 🏗️ **改进方案**

### 1. **重新设计 Platform 扫描**

#### A. 真正的平台级检查
```go
// Platform 扫描不应该挂载主机文件系统
Containers: []corev1.Container{
    {
        Name: "platform-scanner",
        Image: r.getPlatformScannerImage(),
        // 只需要 ServiceAccount 权限访问 K8s API
        Env: []corev1.EnvVar{
            {Name: "CHECK_TYPE", Value: "platform"},
            {Name: "KUBECONFIG", Value: "/var/run/secrets/kubernetes.io/serviceaccount"},
        },
        VolumeMounts: []corev1.VolumeMount{
            {Name: "results", MountPath: "/tmp/results"},
            // 不挂载任何主机文件系统
        },
    },
}
```

#### B. OpenSCAP 的特殊处理
```go
// OpenSCAP 实际上很少有真正的平台级规则
// 大部分 OpenSCAP 规则都是节点级的
// 建议：
// 1. 将 OpenSCAP "platform" 重新归类为 "node"
// 2. 或者创建混合模式处理
```

### 2. **增强节点级扫描**

#### A. 添加节点角色控制
```go
// 根据 Rule.NodeScope 筛选目标节点
func (r *ScanReconciler) filterNodesByScope(allNodes []corev1.Node, nodeScope string) []corev1.Node {
    var targetNodes []corev1.Node
    
    for _, node := range allNodes {
        nodeRole := r.getNodeRole(node)
        
        switch nodeScope {
        case "all", "":
            targetNodes = append(targetNodes, node)
        case "control-plane", "master":
            if nodeRole == "control-plane" || nodeRole == "master" {
                targetNodes = append(targetNodes, node)
            }
        case "worker":
            if nodeRole == "worker" {
                targetNodes = append(targetNodes, node)
            }
        }
    }
    
    return targetNodes
}
```

#### B. 节点角色识别
```go
func (r *ScanReconciler) getNodeRole(node corev1.Node) string {
    // 检查标准的节点角色标签
    if _, exists := node.Labels["node-role.kubernetes.io/control-plane"]; exists {
        return "control-plane"
    }
    if _, exists := node.Labels["node-role.kubernetes.io/master"]; exists {
        return "master"
    }
    return "worker"
}
```

### 3. **STIG 规则映射示例**

#### A. Control Plane 专用规则
```yaml
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-admin-conf-ownership
spec:
  checkType: "node"           # 节点级检查
  nodeScope: "control-plane"  # 只在 Control Plane 执行
  checkScript: |
    #!/bin/bash
    stat -c "%U:%G" /host/etc/kubernetes/admin.conf
```

#### B. Worker 节点专用规则
```yaml
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-worker-kubelet-config
spec:
  checkType: "node"     # 节点级检查
  nodeScope: "worker"   # 只在 Worker 节点执行
  checkScript: |
    #!/bin/bash
    # Worker 特定的检查
    echo "Worker node specific check"
```

#### C. 真正的平台级规则
```yaml
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-rbac-policies
spec:
  checkType: "platform"  # 平台级检查
  # nodeScope: 不适用
  checkScript: |
    #!/bin/bash
    # 通过 K8s API 检查 RBAC
    kubectl get clusterroles --no-headers | wc -l
```

## 📋 **实施步骤**

### 第一阶段：修复 Platform 扫描
1. 移除 Platform 扫描中的主机文件系统挂载
2. 创建专用的 platform-scanner 镜像
3. 更新 Platform 扫描的安全上下文

### 第二阶段：增强 Node 扫描
1. 实现 NodeScope 字段支持
2. 添加节点角色识别逻辑
3. 实现节点筛选功能

### 第三阶段：OpenSCAP 特殊处理
1. 分析现有 OpenSCAP 规则的实际类型
2. 重新分类 OpenSCAP 规则
3. 实现混合扫描模式（如果需要）

### 第四阶段：测试和验证
1. 创建测试用例验证不同扫描类型
2. 验证节点角色筛选功能
3. 确保 STIG 规则正确映射

## 🎯 **预期效果**

1. **概念清晰**：Platform 和 Node 扫描有明确的区别
2. **安全性提升**：Platform 扫描不再需要特权权限
3. **灵活性增强**：支持细粒度的节点角色控制
4. **STIG 兼容**：完美支持 STIG 的节点范围要求
5. **性能优化**：避免在不必要的节点上执行检查
