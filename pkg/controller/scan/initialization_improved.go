// 改进后的扫描初始化逻辑 - 正确实现 ScanType 概念

package scan

import (
	"context"
	"os"

	batchv1 "k8s.io/api/batch/v1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/utils/ptr"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"

	complianceapi "gitlab-ce.alauda.cn/ait/compliance-operator/pkg/apis/compliance/v1alpha1"
)

// createImprovedPlatformScanJobs 创建真正的平台级扫描任务
// 平台级扫描只通过 Kubernetes API 进行集群配置检查，不访问主机文件系统
func (r *ScanReconciler) createImprovedPlatformScanJobs(ctx context.Context, scan *complianceapi.Scan, rules []complianceapi.Rule, scanID string) error {
	r.Log.Info("Creating improved platform scan jobs", "scan", scan.Name, "totalRules", len(rules))

	jobsCreated := 0
	for _, rule := range rules {
		if rule.Spec.CheckType != "platform" {
			continue
		}

		jobName := generateJobName("platform", rule.Name, "")

		job := &batchv1.Job{
			ObjectMeta: metav1.ObjectMeta{
				Name:      jobName,
				Namespace: scan.Namespace,
				Labels: map[string]string{
					"compliance-operator.alauda.io/scan":      scan.Name,
					"compliance-operator.alauda.io/scan-type": "platform",
					"compliance-operator.alauda.io/rule":      rule.Name,
					"compliance-operator.alauda.io/scan-id":   scanID,
					"compliance-operator.alauda.io/temporary": "true",
				},
			},
			Spec: batchv1.JobSpec{
				TTLSecondsAfterFinished: ptr.To[int32](300),
				Template: corev1.PodTemplateSpec{
					ObjectMeta: metav1.ObjectMeta{
						Labels: map[string]string{
							"compliance-operator.alauda.io/scan":      scan.Name,
							"compliance-operator.alauda.io/scan-type": "platform",
							"compliance-operator.alauda.io/rule":      rule.Name,
							"compliance-operator.alauda.io/scan-id":   scanID,
							"compliance-operator.alauda.io/temporary": "true",
						},
					},
					Spec: corev1.PodSpec{
						RestartPolicy:      corev1.RestartPolicyNever,
						ServiceAccountName: "compliance-scanner", // 需要适当的 RBAC 权限
						SecurityContext: &corev1.PodSecurityContext{
							RunAsNonRoot: ptr.To(true),
							RunAsUser:    ptr.To[int64](1000),
							RunAsGroup:   ptr.To[int64](1000),
							FSGroup:      ptr.To[int64](1000),
						},
						Containers: []corev1.Container{
							{
								Name:            "platform-scanner",
								Image:           r.getPlatformScannerImage(),
								ImagePullPolicy: corev1.PullAlways,
								Command:         []string{"/usr/local/bin/platform-scanner.sh"},
								Args:            []string{rule.Spec.CheckScript},
								Env: []corev1.EnvVar{
									{Name: "RULE_ID", Value: rule.Name},
									{Name: "CHECK_TYPE", Value: "platform"},
									{Name: "SCAN_NAME", Value: scan.Name},
									{Name: "NAMESPACE", Value: scan.Namespace},
									{Name: "JOB_NAME", Value: jobName},
									{Name: "SCAN_ID", Value: scanID},
									// 平台级扫描通过 ServiceAccount 访问 K8s API
									{Name: "KUBERNETES_SERVICE_HOST", Value: "kubernetes.default.svc"},
									{Name: "KUBERNETES_SERVICE_PORT", Value: "443"},
								},
								VolumeMounts: []corev1.VolumeMount{
									{
										Name:      "results",
										MountPath: "/tmp/results",
									},
									// 注意：平台级扫描不挂载任何主机文件系统
								},
								SecurityContext: &corev1.SecurityContext{
									AllowPrivilegeEscalation: ptr.To(false),
									ReadOnlyRootFilesystem:   ptr.To(true),
									RunAsNonRoot:             ptr.To(true),
									RunAsUser:                ptr.To[int64](1000),
									Capabilities: &corev1.Capabilities{
										Drop: []corev1.Capability{"ALL"},
									},
								},
								Resources: corev1.ResourceRequirements{
									Requests: corev1.ResourceList{
										corev1.ResourceCPU:    resource.MustParse("100m"),
										corev1.ResourceMemory: resource.MustParse("128Mi"),
									},
									Limits: corev1.ResourceList{
										corev1.ResourceCPU:    resource.MustParse("500m"),
										corev1.ResourceMemory: resource.MustParse("512Mi"),
									},
								},
							},
						},
						Volumes: []corev1.Volume{
							{
								Name: "results",
								VolumeSource: corev1.VolumeSource{
									EmptyDir: &corev1.EmptyDirVolumeSource{},
								},
							},
						},
					},
				},
			},
		}

		if err := controllerutil.SetControllerReference(scan, job, r.Scheme); err != nil {
			return err
		}

		if err := r.Create(ctx, job); err != nil {
			return err
		}

		jobsCreated++
		r.Log.Info("Created platform scan job", "scan", scan.Name, "rule", rule.Name, "jobName", job.Name)
	}

	r.Log.Info("Platform scan jobs creation completed", "scan", scan.Name, "jobsCreated", jobsCreated)
	return nil
}

// createImprovedNodeScanJobs 创建增强的节点级扫描任务，支持节点角色筛选
func (r *ScanReconciler) createImprovedNodeScanJobs(ctx context.Context, scan *complianceapi.Scan, rules []complianceapi.Rule, scanID string) error {
	// 获取所有匹配的节点
	var nodes corev1.NodeList
	if err := r.List(ctx, &nodes, client.MatchingLabels(scan.Spec.NodeSelector)); err != nil {
		return err
	}

	r.Log.Info("Creating improved node scan jobs", "scan", scan.Name, "totalRules", len(rules), "matchingNodes", len(nodes.Items))

	// 根据 NodeScope 分组规则
	ruleGroups := r.groupRulesByNodeScope(rules)

	jobsCreated := 0
	for nodeScope, scopedRules := range ruleGroups {
		// 根据 nodeScope 筛选目标节点
		targetNodes := r.filterNodesByScope(nodes.Items, nodeScope)

		r.Log.Info("Processing node scope", "nodeScope", nodeScope, "rules", len(scopedRules), "targetNodes", len(targetNodes))

		// 为每个目标节点和规则创建扫描 Job
		for _, node := range targetNodes {
			for _, rule := range scopedRules {
				jobName := generateJobName("node", rule.Name, node.Name)

				job := &batchv1.Job{
					ObjectMeta: metav1.ObjectMeta{
						Name:      jobName,
						Namespace: scan.Namespace,
						Labels: map[string]string{
							"compliance-operator.alauda.io/scan":       scan.Name,
							"compliance-operator.alauda.io/scan-type":  "node",
							"compliance-operator.alauda.io/rule":       rule.Name,
							"compliance-operator.alauda.io/node":       node.Name,
							"compliance-operator.alauda.io/node-role":  r.getNodeRole(node),
							"compliance-operator.alauda.io/node-scope": nodeScope,
							"compliance-operator.alauda.io/scan-id":    scanID,
							"compliance-operator.alauda.io/temporary":  "true",
						},
					},
					Spec: batchv1.JobSpec{
						TTLSecondsAfterFinished: ptr.To[int32](300),
						Template: corev1.PodTemplateSpec{
							ObjectMeta: metav1.ObjectMeta{
								Labels: map[string]string{
									"compliance-operator.alauda.io/scan":       scan.Name,
									"compliance-operator.alauda.io/scan-type":  "node",
									"compliance-operator.alauda.io/rule":       rule.Name,
									"compliance-operator.alauda.io/node":       node.Name,
									"compliance-operator.alauda.io/node-role":  r.getNodeRole(node),
									"compliance-operator.alauda.io/node-scope": nodeScope,
									"compliance-operator.alauda.io/scan-id":    scanID,
									"compliance-operator.alauda.io/temporary":  "true",
								},
							},
							Spec: corev1.PodSpec{
								RestartPolicy:      corev1.RestartPolicyNever,
								ServiceAccountName: "compliance-scanner",
								NodeName:           node.Name, // 调度到特定节点
								HostNetwork:        true,      // 节点级扫描需要访问主机网络
								HostPID:            true,      // 需要访问主机进程
								HostIPC:            true,      // 需要访问主机 IPC
								SecurityContext: &corev1.PodSecurityContext{
									RunAsUser: ptr.To[int64](0), // 节点级扫描需要 root 权限
								},
								Containers: []corev1.Container{
									{
										Name:            "node-scanner",
										Image:           r.getNodeScannerImage(),
										ImagePullPolicy: corev1.PullAlways,
										Command:         []string{"/usr/local/bin/node-scanner.sh"},
										Args:            []string{rule.Spec.CheckScript},
										Env: []corev1.EnvVar{
											{Name: "RULE_ID", Value: rule.Name},
											{Name: "CHECK_TYPE", Value: "node"},
											{Name: "NODE_SCOPE", Value: nodeScope},
											{Name: "NODE_ROLE", Value: r.getNodeRole(node)},
											{Name: "SCAN_NAME", Value: scan.Name},
											{Name: "NAMESPACE", Value: scan.Namespace},
											{Name: "JOB_NAME", Value: jobName},
											{Name: "SCAN_ID", Value: scanID},
											{Name: "NODE_NAME", Value: node.Name},
										},
										SecurityContext: &corev1.SecurityContext{
											Privileged: ptr.To(true), // 节点级扫描需要特权权限
										},
										VolumeMounts: []corev1.VolumeMount{
											{
												Name:      "host-root",
												MountPath: "/host",
												ReadOnly:  true,
											},
											{
												Name:      "results",
												MountPath: "/tmp/results",
											},
										},
									},
								},
								Volumes: []corev1.Volume{
									{
										Name: "host-root",
										VolumeSource: corev1.VolumeSource{
											HostPath: &corev1.HostPathVolumeSource{
												Path: "/",
											},
										},
									},
									{
										Name: "results",
										VolumeSource: corev1.VolumeSource{
											EmptyDir: &corev1.EmptyDirVolumeSource{},
										},
									},
								},
							},
						},
					},
				}

				if err := controllerutil.SetControllerReference(scan, job, r.Scheme); err != nil {
					return err
				}

				if err := r.Create(ctx, job); err != nil {
					return err
				}

				jobsCreated++
			}
		}
	}

	r.Log.Info("Node scan jobs creation completed", "scan", scan.Name, "jobsCreated", jobsCreated)
	return nil
}

// groupRulesByNodeScope 根据 NodeScope 分组规则
func (r *ScanReconciler) groupRulesByNodeScope(rules []complianceapi.Rule) map[string][]complianceapi.Rule {
	groups := make(map[string][]complianceapi.Rule)

	for _, rule := range rules {
		if rule.Spec.CheckType != "node" {
			continue // 只处理节点级规则
		}

		nodeScope := rule.Spec.NodeScope
		if nodeScope == "" {
			nodeScope = "all" // 默认为所有节点
		}

		groups[nodeScope] = append(groups[nodeScope], rule)
	}

	return groups
}

// filterNodesByScope 根据 NodeScope 筛选节点
func (r *ScanReconciler) filterNodesByScope(allNodes []corev1.Node, nodeScope string) []corev1.Node {
	var targetNodes []corev1.Node

	for _, node := range allNodes {
		nodeRole := r.getNodeRole(node)

		switch nodeScope {
		case "all", "":
			targetNodes = append(targetNodes, node)
		case "control-plane", "master":
			if nodeRole == "control-plane" || nodeRole == "master" {
				targetNodes = append(targetNodes, node)
			}
		case "worker":
			if nodeRole == "worker" {
				targetNodes = append(targetNodes, node)
			}
		}
	}

	return targetNodes
}

// getNodeRole 获取节点角色
func (r *ScanReconciler) getNodeRole(node corev1.Node) string {
	// 检查标准的节点角色标签
	if _, exists := node.Labels["node-role.kubernetes.io/control-plane"]; exists {
		return "control-plane"
	}
	if _, exists := node.Labels["node-role.kubernetes.io/master"]; exists {
		return "master"
	}
	// 检查旧版本的标签
	if _, exists := node.Labels["kubernetes.io/role"]; exists {
		return node.Labels["kubernetes.io/role"]
	}
	return "worker"
}

// getPlatformScannerImage 获取平台扫描器镜像
func (r *ScanReconciler) getPlatformScannerImage() string {
	if image := os.Getenv("PLATFORM_SCANNER_IMAGE"); image != "" {
		return image
	}
	return "registry.alauda.cn:60070/test/compliance/platform-scanner:latest"
}
