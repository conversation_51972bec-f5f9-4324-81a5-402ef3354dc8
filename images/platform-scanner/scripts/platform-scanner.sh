#!/bin/bash

# Platform Scanner Script - 真正的平台级扫描
# 只通过 Kubernetes API 进行集群级配置检查，不访问主机文件系统

set -euo pipefail

# 环境变量
RULE_ID="${RULE_ID:-unknown}"
CHECK_TYPE="${CHECK_TYPE:-platform}"
SCAN_NAME="${SCAN_NAME:-unknown}"
NAMESPACE="${NAMESPACE:-default}"
JOB_NAME="${JOB_NAME:-unknown}"
SCAN_ID="${SCAN_ID:-unknown}"
RESULTS_DIR="${RESULTS_DIR:-/tmp/results}"

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $*" >&2
}

log "Starting platform scanner"
log "Rule ID: $RULE_ID"
log "Check Type: $CHECK_TYPE"
log "Scan Name: $SCAN_NAME"
log "Job Name: $JOB_NAME"

# 确保结果目录存在
mkdir -p "$RESULTS_DIR"

# 结果文件
RESULT_FILE="$RESULTS_DIR/${RULE_ID}_result.json"
OUTPUT_FILE="$RESULTS_DIR/${RULE_ID}_output.txt"

# 检查 kubectl 是否可用
if ! command -v kubectl &> /dev/null; then
    log "ERROR: kubectl not found"
    exit 1
fi

# 检查 Kubernetes API 连接
if ! kubectl cluster-info &> /dev/null; then
    log "ERROR: Cannot connect to Kubernetes API"
    exit 1
fi

log "Kubernetes API connection verified"

# 执行平台级检查脚本
log "Executing platform check script for rule: $RULE_ID"

# 创建临时脚本文件
TEMP_SCRIPT="/tmp/check_script.sh"
cat > "$TEMP_SCRIPT" << 'EOF'
#!/bin/bash
# 这里会被传入的检查脚本替换
EOF

# 将传入的检查脚本写入临时文件
if [ $# -gt 0 ]; then
    echo "$1" > "$TEMP_SCRIPT"
else
    log "ERROR: No check script provided"
    exit 1
fi

chmod +x "$TEMP_SCRIPT"

# 执行检查脚本并捕获输出和退出码
EXIT_CODE=0
OUTPUT=""

log "Running platform check script..."
if OUTPUT=$("$TEMP_SCRIPT" 2>&1); then
    EXIT_CODE=0
    log "Platform check completed successfully"
else
    EXIT_CODE=$?
    log "Platform check failed with exit code: $EXIT_CODE"
fi

# 保存输出到文件
echo "$OUTPUT" > "$OUTPUT_FILE"

# 确定检查状态
STATUS="PASS"
if [ $EXIT_CODE -ne 0 ]; then
    STATUS="FAIL"
fi

log "Check result: $STATUS (exit code: $EXIT_CODE)"

# 生成结果 JSON
cat > "$RESULT_FILE" << EOF
{
    "ruleId": "$RULE_ID",
    "checkType": "$CHECK_TYPE",
    "status": "$STATUS",
    "exitCode": $EXIT_CODE,
    "output": $(echo "$OUTPUT" | jq -R -s .),
    "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
    "scanId": "$SCAN_ID",
    "jobName": "$JOB_NAME",
    "scanName": "$SCAN_NAME",
    "namespace": "$NAMESPACE"
}
EOF

log "Results saved to: $RESULT_FILE"

# 清理临时文件
rm -f "$TEMP_SCRIPT"

log "Platform scanner completed"
exit $EXIT_CODE
