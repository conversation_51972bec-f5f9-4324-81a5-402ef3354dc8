# Platform Scanner Image - 专用于平台级合规检查
# 只包含 kubectl 和基本工具，不需要主机文件系统访问

FROM alpine:3.18

# 安装必要的工具
RUN apk add --no-cache \
    bash \
    curl \
    jq \
    ca-certificates \
    && rm -rf /var/cache/apk/*

# 安装 kubectl
ARG KUBECTL_VERSION=v1.28.0
RUN curl -LO "https://dl.k8s.io/release/${KUBECTL_VERSION}/bin/linux/amd64/kubectl" \
    && chmod +x kubectl \
    && mv kubectl /usr/local/bin/

# 创建非特权用户
RUN addgroup -g 1000 scanner \
    && adduser -D -u 1000 -G scanner scanner

# 复制脚本
COPY scripts/platform-scanner.sh /usr/local/bin/platform-scanner.sh
RUN chmod +x /usr/local/bin/platform-scanner.sh

# 创建结果目录
RUN mkdir -p /tmp/results \
    && chown -R scanner:scanner /tmp/results

# 切换到非特权用户
USER scanner

# 设置工作目录
WORKDIR /tmp

# 默认入口点
ENTRYPOINT ["/usr/local/bin/platform-scanner.sh"]
