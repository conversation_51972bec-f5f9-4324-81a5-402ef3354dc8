# OpenSCAP 离线扫描解决方案

本文档描述了如何解决 OpenSCAP 数据流文件依赖远程资源的问题，实现完全离线的合规性扫描。

## 问题描述

在使用 OpenSCAP 扫描 SUSE Linux Enterprise Micro 5 时，数据流文件 `ssg-slmicro5-ds.xml` 依赖远程的 OVAL 文件：

```
https://ftp.suse.com/pub/projects/security/oval/suse.linux.enterprise.micro.5-patch.xml.bz2
```

这导致在离线环境中扫描时出现以下错误：

```
WARNING: Datastream component 'scap_org.open-scap_cref_pub-projects-security-oval-suse.linux.enterprise.micro.5-patch.xml.bz2' points out to the remote 'https://ftp.suse.com/pub/projects/security/oval/suse.linux.enterprise.micro.5-patch.xml.bz2'. Use '--fetch-remote-resources' option to download it.
WARNING: Skipping 'https://ftp.suse.com/pub/projects/security/oval/suse.linux.enterprise.micro.5-patch.xml.bz2' file which is referenced from datastream
OpenSCAP Error: Could not extract scap_org.open-scap_cref_ssg-slmicro5-xccdf.xml with all dependencies from datastream.
```

## 解决方案

### 1. 自动依赖下载和集成

我们创建了两个脚本来自动处理远程依赖：

#### `scripts/download-dependencies.sh`
- 下载远程 OVAL 依赖文件
- 自动解压缩 `.bz2` 文件
- 验证下载的文件完整性
- 创建修改后的离线数据流文件

#### `scripts/integrate-dependencies.py`
- 分析数据流文件中的远程引用
- 创建包含本地文件引用的离线版本
- 支持两种模式：
  - **离线模式**：将远程引用替换为本地文件引用
  - **嵌入模式**：将依赖直接嵌入到数据流文件中

### 2. 镜像构建集成

更新后的 `Dockerfile` 在构建时自动：
1. 安装必要的工具（curl, bzip2, xmllint, python3）
2. 下载远程依赖文件
3. 创建离线版本的数据流文件
4. 验证文件完整性

### 3. Scanner 脚本增强

更新后的 `openscap-scanner.sh` 脚本：
- 优先使用离线版本的数据流文件（`*-offline.xml`）
- 设置适当的环境变量帮助 OpenSCAP 找到本地依赖
- 提供详细的日志输出，显示使用的文件类型

## 使用方法

### 构建镜像

```bash
# 构建 content 镜像（包含离线依赖）
cd images/content
docker build -t compliance-content:offline .

# 构建 scanner 镜像
cd ../openscap-scanner
docker build -t compliance-scanner:offline .
```

### 运行离线扫描

```bash
# 使用离线数据流进行扫描
docker run --privileged \
  -v /:/host \
  -e HOSTROOT=/host \
  -e PROFILE=xccdf_org.ssgproject.content_profile_standard \
  -e CONTENT=ssg-slmicro5-ds.xml \
  -e REPORT_DIR=/reports \
  --volumes-from compliance-content:offline \
  compliance-scanner:offline
```

### 验证离线功能

使用提供的测试脚本验证离线扫描功能：

```bash
./test-offline-scanning.sh
```

## 文件结构

```
images/content/
├── Dockerfile                          # 更新的构建文件
├── README-OFFLINE.md                   # 本文档
├── ds/
│   ├── ssg-slmicro5-ds.xml            # 原始数据流文件
│   ├── ssg-slmicro5-ds-offline.xml    # 离线版本（构建时生成）
│   └── ssg-ubuntu2204-ds.xml          # Ubuntu 数据流文件
├── scripts/
│   ├── download-dependencies.sh        # 依赖下载脚本
│   └── integrate-dependencies.py       # 依赖集成脚本
└── suse.linux.enterprise.micro.5-patch.xml  # 本地 OVAL 文件（构建时下载）
```

## 技术细节

### 依赖文件处理

1. **下载**：从 SUSE FTP 服务器下载 OVAL 文件
2. **解压**：自动处理 `.bz2` 压缩文件
3. **验证**：使用 `xmllint` 验证 XML 文件格式
4. **集成**：修改数据流文件中的远程引用

### 离线数据流创建

原始引用：
```xml
<ds:component-ref id="scap_org.open-scap_cref_pub-projects-security-oval-suse.linux.enterprise.micro.5-patch.xml.bz2" 
                  xlink:href="https://ftp.suse.com/pub/projects/security/oval/suse.linux.enterprise.micro.5-patch.xml.bz2"/>
```

离线引用：
```xml
<ds:component-ref id="scap_org.open-scap_cref_pub-projects-security-oval-suse.linux.enterprise.micro.5-patch.xml.bz2" 
                  xlink:href="suse.linux.enterprise.micro.5-patch.xml"/>
```

### 环境变量

Scanner 脚本设置以下环境变量来帮助 OpenSCAP 找到本地文件：
- `OSCAP_OVAL_RESULTS_DIRECTORY`：OVAL 结果目录
- `OSCAP_DATASTREAM_DIRECTORY`：数据流文件目录

## 故障排除

### 常见问题

1. **依赖下载失败**
   - 检查网络连接
   - 验证 SUSE FTP 服务器可访问性
   - 检查防火墙设置

2. **XML 验证失败**
   - 检查下载的文件完整性
   - 验证文件未损坏
   - 重新下载依赖文件

3. **离线扫描仍然失败**
   - 确认使用的是 `*-offline.xml` 文件
   - 检查本地依赖文件是否存在
   - 验证文件权限

### 调试命令

```bash
# 检查容器中的文件
docker run --rm compliance-content:offline ls -la /content/

# 测试数据流信息
docker run --rm compliance-content:offline oscap info /content/ssg-slmicro5-ds-offline.xml

# 检查依赖文件
docker run --rm compliance-content:offline ls -la /content/suse.linux.enterprise.micro.5-patch.xml
```

## 注意事项

1. **文件大小**：离线版本会增加镜像大小，因为包含了本地依赖文件
2. **更新频率**：需要定期重新构建镜像以获取最新的 OVAL 定义
3. **兼容性**：确保 OpenSCAP 版本与数据流文件兼容
4. **安全性**：定期更新依赖文件以获取最新的安全定义

## 支持的数据流

目前支持以下数据流的离线处理：
- SUSE Linux Enterprise Micro 5 (`ssg-slmicro5-ds.xml`)

可以扩展脚本来支持其他有远程依赖的数据流文件。
