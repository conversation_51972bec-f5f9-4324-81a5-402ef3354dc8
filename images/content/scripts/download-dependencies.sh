#!/bin/bash

set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

log_error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

log_success() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] SUCCESS: $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

# Configuration
WORK_DIR="/tmp/openscap-deps"
CONTENT_DIR="/content"
DS_FILE="ssg-slmicro5-ds.xml"

# Remote dependencies for SLE Micro 5
REMOTE_DEPS=(
    "https://ftp.suse.com/pub/projects/security/oval/suse.linux.enterprise.micro.5-patch.xml.bz2"
)

# Create working directory
setup_work_dir() {
    log "Setting up working directory: $WORK_DIR"
    mkdir -p "$WORK_DIR"
    cd "$WORK_DIR"
}

# Download remote dependencies
download_dependencies() {
    log "Downloading remote dependencies..."
    
    for url in "${REMOTE_DEPS[@]}"; do
        local filename=$(basename "$url")
        local local_file="$WORK_DIR/$filename"
        
        log "Downloading: $url"
        
        if curl -L -o "$local_file" "$url"; then
            log_success "Downloaded: $filename"
            
            # If it's a compressed file, decompress it
            if [[ "$filename" == *.bz2 ]]; then
                local decompressed="${filename%.bz2}"
                log "Decompressing: $filename -> $decompressed"
                
                if bunzip2 -k "$local_file"; then
                    log_success "Decompressed: $decompressed"
                    # Remove the compressed version to save space
                    rm -f "$local_file"
                else
                    log_error "Failed to decompress: $filename"
                    return 1
                fi
            fi
        else
            log_error "Failed to download: $url"
            return 1
        fi
    done
}

# Validate downloaded files
validate_dependencies() {
    log "Validating downloaded dependencies..."
    
    for url in "${REMOTE_DEPS[@]}"; do
        local filename=$(basename "$url")
        local expected_file
        
        if [[ "$filename" == *.bz2 ]]; then
            expected_file="$WORK_DIR/${filename%.bz2}"
        else
            expected_file="$WORK_DIR/$filename"
        fi
        
        if [[ -f "$expected_file" ]]; then
            local file_size=$(stat -c%s "$expected_file" 2>/dev/null || echo "0")
            log_success "Found: $(basename "$expected_file") (${file_size} bytes)"
            
            # Basic XML validation
            if [[ "$expected_file" == *.xml ]]; then
                if xmllint --noout "$expected_file" 2>/dev/null; then
                    log_success "XML validation passed: $(basename "$expected_file")"
                else
                    log_warning "XML validation failed: $(basename "$expected_file")"
                fi
            fi
        else
            log_error "Missing expected file: $expected_file"
            return 1
        fi
    done
}

# Create a modified datastream with local references
create_offline_datastream() {
    log "Creating offline datastream..."
    
    local input_ds="$CONTENT_DIR/$DS_FILE"
    local output_ds="$CONTENT_DIR/${DS_FILE%.xml}-offline.xml"
    local temp_ds="$WORK_DIR/temp-ds.xml"
    
    if [[ ! -f "$input_ds" ]]; then
        log_error "Input datastream not found: $input_ds"
        return 1
    fi
    
    # Copy original datastream to temp location
    cp "$input_ds" "$temp_ds"
    
    # Replace remote URLs with local file references
    log "Replacing remote URLs with local references..."
    
    # Replace the SUSE OVAL URL with local reference
    sed -i 's|https://ftp.suse.com/pub/projects/security/oval/suse.linux.enterprise.micro.5-patch.xml.bz2|suse.linux.enterprise.micro.5-patch.xml|g' "$temp_ds"
    
    # Copy the modified datastream back
    cp "$temp_ds" "$output_ds"
    
    log_success "Created offline datastream: $output_ds"
    
    # Validate the modified datastream
    if xmllint --noout "$output_ds" 2>/dev/null; then
        log_success "Offline datastream XML validation passed"
    else
        log_error "Offline datastream XML validation failed"
        return 1
    fi
}

# Copy dependencies to content directory
install_dependencies() {
    log "Installing dependencies to content directory..."
    
    for url in "${REMOTE_DEPS[@]}"; do
        local filename=$(basename "$url")
        local source_file
        local dest_file
        
        if [[ "$filename" == *.bz2 ]]; then
            source_file="$WORK_DIR/${filename%.bz2}"
            dest_file="$CONTENT_DIR/${filename%.bz2}"
        else
            source_file="$WORK_DIR/$filename"
            dest_file="$CONTENT_DIR/$filename"
        fi
        
        if [[ -f "$source_file" ]]; then
            cp "$source_file" "$dest_file"
            log_success "Installed: $(basename "$dest_file")"
        else
            log_error "Source file not found: $source_file"
            return 1
        fi
    done
}

# Cleanup
cleanup() {
    log "Cleaning up temporary files..."
    rm -rf "$WORK_DIR"
    log_success "Cleanup completed"
}

# Main execution
main() {
    log "OpenSCAP Dependencies Downloader starting..."
    
    # Check required tools
    for tool in curl bunzip2 xmllint; do
        if ! command -v "$tool" &> /dev/null; then
            log_error "Required tool not found: $tool"
            exit 1
        fi
    done
    
    # Execute steps
    setup_work_dir
    download_dependencies
    validate_dependencies
    create_offline_datastream
    install_dependencies
    cleanup
    
    log_success "OpenSCAP dependencies download completed successfully!"
    log "Available files in $CONTENT_DIR:"
    ls -la "$CONTENT_DIR/"
}

# Execute main function
main "$@"
