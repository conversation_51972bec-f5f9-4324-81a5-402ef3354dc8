#!/usr/bin/env python3
"""
OpenSCAP Datastream Dependencies Integrator

This script integrates remote OVAL dependencies into OpenSCAP datastreams
to enable offline scanning without --fetch-remote-resources.
"""

import os
import sys
import xml.etree.ElementTree as ET
import urllib.parse
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DatastreamIntegrator:
    """Integrates remote dependencies into OpenSCAP datastreams."""
    
    def __init__(self, content_dir="/content"):
        self.content_dir = Path(content_dir)
        self.namespaces = {
            'ds': 'http://scap.nist.gov/schema/scap/source/1.2',
            'xlink': 'http://www.w3.org/1999/xlink',
            'oval-def': 'http://oval.mitre.org/XMLSchema/oval-definitions-5'
        }
        
    def find_remote_references(self, datastream_file):
        """Find all remote references in the datastream."""
        logger.info(f"Analyzing datastream: {datastream_file}")
        
        try:
            tree = ET.parse(datastream_file)
            root = tree.getroot()
            
            # Register namespaces
            for prefix, uri in self.namespaces.items():
                ET.register_namespace(prefix, uri)
            
            remote_refs = []
            
            # Find component-ref elements with remote xlink:href
            for comp_ref in root.findall('.//ds:component-ref', self.namespaces):
                href = comp_ref.get('{http://www.w3.org/1999/xlink}href', '')
                if href.startswith('http://') or href.startswith('https://'):
                    remote_refs.append({
                        'element': comp_ref,
                        'url': href,
                        'id': comp_ref.get('id', ''),
                        'local_filename': self._url_to_filename(href)
                    })
                    logger.info(f"Found remote reference: {href}")
            
            return tree, remote_refs
            
        except ET.ParseError as e:
            logger.error(f"Failed to parse XML: {e}")
            return None, []
        except Exception as e:
            logger.error(f"Error analyzing datastream: {e}")
            return None, []
    
    def _url_to_filename(self, url):
        """Convert URL to local filename."""
        parsed = urllib.parse.urlparse(url)
        filename = os.path.basename(parsed.path)
        
        # Remove compression extensions for local reference
        if filename.endswith('.bz2'):
            filename = filename[:-4]
        elif filename.endswith('.gz'):
            filename = filename[:-3]
        
        return filename
    
    def create_embedded_datastream(self, datastream_file, output_file=None):
        """Create a datastream with embedded dependencies."""
        if output_file is None:
            base_name = datastream_file.stem
            output_file = datastream_file.parent / f"{base_name}-embedded.xml"
        
        logger.info(f"Creating embedded datastream: {output_file}")
        
        tree, remote_refs = self.find_remote_references(datastream_file)
        if tree is None:
            return False
        
        root = tree.getroot()
        
        # Process each remote reference
        for ref_info in remote_refs:
            local_file = self.content_dir / ref_info['local_filename']
            
            if not local_file.exists():
                logger.warning(f"Local dependency not found: {local_file}")
                continue
            
            logger.info(f"Embedding: {local_file}")
            
            try:
                # Parse the dependency file
                dep_tree = ET.parse(local_file)
                dep_root = dep_tree.getroot()
                
                # Create a new component element
                component_id = f"scap_org.open-scap_comp_{ref_info['local_filename'].replace('.', '_').replace('-', '_')}"
                
                # Find the data-stream element
                data_stream = root.find('.//ds:data-stream', self.namespaces)
                if data_stream is None:
                    logger.error("Could not find data-stream element")
                    continue
                
                # Create new component
                new_component = ET.Element(
                    f"{{{self.namespaces['ds']}}}component",
                    id=component_id,
                    timestamp="2025-06-30T00:00:00"
                )
                
                # Add the dependency content to the component
                new_component.append(dep_root)
                
                # Add the component to the datastream collection
                root.append(new_component)
                
                # Update the component-ref to point to the new embedded component
                ref_info['element'].set(
                    '{http://www.w3.org/1999/xlink}href',
                    f"#{component_id}"
                )
                
                logger.info(f"Embedded component: {component_id}")
                
            except Exception as e:
                logger.error(f"Failed to embed {local_file}: {e}")
                continue
        
        # Write the modified datastream
        try:
            tree.write(
                output_file,
                encoding='utf-8',
                xml_declaration=True,
                method='xml'
            )
            logger.info(f"Created embedded datastream: {output_file}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to write embedded datastream: {e}")
            return False
    
    def create_offline_datastream(self, datastream_file, output_file=None):
        """Create a datastream with local file references."""
        if output_file is None:
            base_name = datastream_file.stem
            output_file = datastream_file.parent / f"{base_name}-offline.xml"
        
        logger.info(f"Creating offline datastream: {output_file}")
        
        tree, remote_refs = self.find_remote_references(datastream_file)
        if tree is None:
            return False
        
        # Update remote references to local files
        for ref_info in remote_refs:
            local_file = self.content_dir / ref_info['local_filename']
            
            if local_file.exists():
                # Update href to point to local file
                ref_info['element'].set(
                    '{http://www.w3.org/1999/xlink}href',
                    ref_info['local_filename']
                )
                logger.info(f"Updated reference: {ref_info['url']} -> {ref_info['local_filename']}")
            else:
                logger.warning(f"Local dependency not found: {local_file}")
        
        # Write the modified datastream
        try:
            tree.write(
                output_file,
                encoding='utf-8',
                xml_declaration=True,
                method='xml'
            )
            logger.info(f"Created offline datastream: {output_file}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to write offline datastream: {e}")
            return False

def main():
    """Main execution function."""
    content_dir = os.environ.get('CONTENT_DIR', '/content')
    integrator = DatastreamIntegrator(content_dir)
    
    # Process all datastream files
    datastream_files = list(Path(content_dir).glob('ssg-*-ds.xml'))
    
    if not datastream_files:
        logger.error(f"No datastream files found in {content_dir}")
        return 1
    
    success_count = 0
    
    for ds_file in datastream_files:
        logger.info(f"Processing: {ds_file}")
        
        # Create offline version (with local file references)
        if integrator.create_offline_datastream(ds_file):
            success_count += 1
        
        # Optionally create embedded version (dependencies embedded in XML)
        # This creates larger files but is more self-contained
        # if integrator.create_embedded_datastream(ds_file):
        #     success_count += 1
    
    logger.info(f"Successfully processed {success_count} datastream files")
    return 0 if success_count > 0 else 1

if __name__ == '__main__':
    sys.exit(main())
