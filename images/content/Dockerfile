FROM alpine:3.18

# Install required tools for dependency processing
RUN apk add --no-cache \
    curl \
    bzip2 \
    libxml2-utils \
    python3 \
    py3-pip \
    bash

# Create directories
RUN mkdir -p /content /scripts

# Copy scripts
COPY scripts/download-dependencies.sh /scripts/
COPY scripts/integrate-dependencies.py /scripts/

# Make scripts executable
RUN chmod +x /scripts/download-dependencies.sh /scripts/integrate-dependencies.py

# Copy XML datastream files
COPY ds/*.xml /content/

# Set working directory
WORKDIR /content

# Download and integrate dependencies during build
RUN /scripts/download-dependencies.sh && \
    python3 /scripts/integrate-dependencies.py

# List final content for verification
RUN echo "=== Final content directory ===" && \
    ls -la /content/ && \
    echo "=== Checking for offline datastreams ===" && \
    ls -la /content/*offline*.xml 2>/dev/null || echo "No offline datastreams found"

# Use a simple command to keep container running if needed
CMD ["ls", "-la", "/content"]