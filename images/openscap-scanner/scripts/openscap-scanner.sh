#!/bin/bash

set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
HOSTROOT=${HOSTROOT:-/host}
REPORT_DIR=${REPORT_DIR:-/reports}
CONTENT_DIR=${CONTENT_DIR:-/content}
PROFILE=${PROFILE:-""}
CONTENT=${CONTENT:-""}
CONTENT_IMAGE=${CONTENT_IMAGE:-""}
NODE_NAME=${NODE_NAME:-$(hostname)}
SCAN_ID=${SCAN_ID:-$(date +%s)}

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

log_error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

log_success() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] SUCCESS: $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

# Validate required environment variables
validate_environment() {
    log "Validating environment variables..."
    
    if [[ -z "$PROFILE" ]]; then
        log_error "PROFILE environment variable is required"
        exit 1
    fi
    
    if [[ -z "$CONTENT" ]]; then
        log_error "CONTENT environment variable is required"
        exit 1
    fi
    
    log_success "Environment variables validated"
}

# Check OpenSCAP installation
check_openscap() {
    log "Checking OpenSCAP installation..."
    
    if ! command -v oscap &> /dev/null; then
        log_error "OpenSCAP (oscap) not found"
        exit 1
    fi
    
    local version=$(oscap --version | head -n1)
    log_success "OpenSCAP found: $version"
}

# Setup CPE dictionary to fix CPE session errors
setup_cpe_dict() {
    log "Setting up CPE dictionary..."
    
    # Create CPE directory if it doesn't exist
    mkdir -p /usr/share/openscap/cpe
    
    # Create a minimal CPE dictionary if it doesn't exist
    if [[ ! -f "/usr/share/openscap/cpe/openscap-cpe-dict.xml" ]]; then
        cat > /usr/share/openscap/cpe/openscap-cpe-dict.xml << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<cpe-list xmlns="http://cpe.mitre.org/dictionary/2.0" 
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
          xsi:schemaLocation="http://cpe.mitre.org/dictionary/2.0 http://cpe.mitre.org/files/cpe-dictionary_2.1.xsd">
    <generator>
        <product_name>OpenSCAP Scanner</product_name>
        <product_version>1.0</product_version>
        <schema_version>2.1</schema_version>
        <timestamp>2025-01-01T00:00:00</timestamp>
    </generator>
</cpe-list>
EOF
        log_success "Created minimal CPE dictionary"
    else
        log_success "CPE dictionary already exists"
    fi
}

# Setup offline scanning environment
setup_offline_scanning_environment() {
    log "Setting up offline scanning environment..."

    # Check if we have local dependencies available
    local deps_available=true
    local content_dir_to_check="${CONTENT_DIR}"

    # If using shared content, check there too
    if [[ -d "/shared-content" ]]; then
        content_dir_to_check="/shared-content"
    fi

    # Check for SUSE OVAL dependencies
    local suse_oval="suse.linux.enterprise.micro.5-patch.xml"
    if [[ -f "${content_dir_to_check}/${suse_oval}" ]]; then
        log_success "Found local SUSE OVAL dependency: ${content_dir_to_check}/${suse_oval}"

        # Set environment variable to help OpenSCAP find local files
        export OSCAP_OVAL_RESULTS_DIRECTORY="${content_dir_to_check}"
        export OSCAP_DATASTREAM_DIRECTORY="${content_dir_to_check}"

        log "Set OSCAP_OVAL_RESULTS_DIRECTORY to: $OSCAP_OVAL_RESULTS_DIRECTORY"
        log "Set OSCAP_DATASTREAM_DIRECTORY to: $OSCAP_DATASTREAM_DIRECTORY"
    else
        log_warning "Local SUSE OVAL dependency not found: ${content_dir_to_check}/${suse_oval}"
        log_warning "Scanning may require --fetch-remote-resources option"
        deps_available=false
    fi

    if $deps_available; then
        log_success "Offline scanning environment configured successfully"
    else
        log_warning "Offline scanning environment setup incomplete"
    fi

    return 0
}

# Enhanced environment setup for better host scanning
setup_host_scanning_environment() {
    log "Setting up enhanced environment for host scanning..."

    # Verify host root accessibility
    if [[ ! -d "$HOSTROOT" ]]; then
        log_error "Host root directory not accessible: $HOSTROOT"
        exit 1
    fi
    
    # Check key directories
    for dir in "/etc" "/var" "/usr"; do
        if [[ -d "${HOSTROOT}${dir}" ]]; then
            log "Host directory accessible: ${HOSTROOT}${dir}"
        else
            log_warning "Host directory not found: ${HOSTROOT}${dir}"
        fi
    done
    
    log_success "Host scanning environment configured"
    
    # Set environment variables for OpenSCAP probes
    export OSCAP_PROBE_ROOT="$HOSTROOT"
    export OSCAP_PROBE_ARCHITECTURE=$(uname -m)
    
    # Try to improve file system device access
    if [[ -r "${HOSTROOT}/proc/filesystems" ]]; then
        export OSCAP_PROBE_FILESYSTEMS="${HOSTROOT}/proc/filesystems"
        log "Using host filesystems info: ${HOSTROOT}/proc/filesystems"
    fi
    
    # Set up mount info for better device detection
    if [[ -r "${HOSTROOT}/proc/mounts" ]]; then
        export OSCAP_PROBE_MOUNTS="${HOSTROOT}/proc/mounts"
        log "Using host mount info: ${HOSTROOT}/proc/mounts"
    fi
    
    log "OSCAP_PROBE_ROOT set to: $OSCAP_PROBE_ROOT"
    log "Using enhanced probe environment for host scanning"
}

# Check for pre-extracted content
check_content() {
    local content_file
    local offline_content="${CONTENT%.xml}-offline.xml"

    # First, try to find offline version (preferred for offline scanning)
    if [[ -f "${CONTENT_DIR}/${offline_content}" ]]; then
        content_file="${CONTENT_DIR}/${offline_content}"
        log_success "Found offline datastream: $content_file"
    elif [[ -f "/shared-content/${offline_content}" ]]; then
        content_file="/shared-content/${offline_content}"
        log_success "Found offline datastream: $content_file"
    # Fallback to original content file
    elif [[ -f "${CONTENT_DIR}/${CONTENT}" ]]; then
        content_file="${CONTENT_DIR}/${CONTENT}"
        log_warning "Using original datastream (may require --fetch-remote-resources): $content_file"
    elif [[ -f "/shared-content/${CONTENT}" ]]; then
        content_file="/shared-content/${CONTENT}"
        log_warning "Using original datastream (may require --fetch-remote-resources): $content_file"
    else
        log_error "Content file not found: $CONTENT"
        log_error "Searched for offline version: $offline_content"
        log_error "Searched for original version: $CONTENT"
        log_error "Searched in: ${CONTENT_DIR}/ and /shared-content/"
        exit 1
    fi
    
    # Check file permissions and readability
    if [[ ! -r "$content_file" ]]; then
        log_error "Content file is not readable: $content_file"
        ls -la "$content_file" >&2 || true
        exit 1
    fi
    
    # Check file size
    local file_size=$(stat -c%s "$content_file" 2>/dev/null || echo "unknown")
    
    # Output all logs to stderr to avoid mixing with return value
    log "Checking for pre-extracted content..." >&2
    log_success "Content file found: $content_file" >&2
    log "Content file size: $file_size bytes" >&2
    
    # Return only the file path to stdout
    echo "$content_file"
}

# List available profiles with better error handling
list_profiles() {
    log "Listing available profiles in datastream..."
    
    local content_file="$1"
    
    # Use a more robust approach to list profiles
    log "Available profiles:"
    if oscap info --profiles "$content_file" 2>/dev/null; then
        log_success "Profiles listed successfully"
    else
        # Try alternative approach
        if oscap info "$content_file" 2>/dev/null | grep -A 10 "Profiles:" || true; then
            log_warning "Profiles listed with warnings (this is normal)"
        else
            log_warning "Could not list profiles, but this may not affect scanning"
        fi
    fi
}

# Run OpenSCAP evaluation with enhanced error handling
run_evaluation() {
    log "Starting OpenSCAP evaluation..."
    
    local content_file="$1"
    local report_file="${REPORT_DIR}/report-${NODE_NAME}-${SCAN_ID}.html"
    local results_file="${REPORT_DIR}/results-${NODE_NAME}-${SCAN_ID}.xml"
    
    # Create reports directory
    mkdir -p "$REPORT_DIR"
    
    # Suppress RPM warnings for non-RPM systems
    export OSCAP_PROBE_RPM_VERIFY_IGNORE_ERRORS=1
    
    log "Content file: $content_file"
    log "Report file: $report_file"
    log "Results file: $results_file"
    log "Environment: OSCAP_PROBE_ROOT=$OSCAP_PROBE_ROOT"
    
    # Execute oscap command directly without eval to avoid shell parsing issues
    local exit_code=0
    log "Executing OpenSCAP evaluation..."
    
    oscap xccdf eval \
        --profile "$PROFILE" \
        --report "$report_file" \
        --results "$results_file" \
        --oval-results \
        --check-engine-results \
        "$content_file" || exit_code=$?
    
    # Handle different exit codes
    case $exit_code in
        0)
            log_success "OpenSCAP evaluation completed successfully - all rules passed"
            ;;
        1)
            log_error "OpenSCAP evaluation failed due to an error"
            return 1
            ;;
        2)
            log_warning "OpenSCAP evaluation completed with non-compliance findings (exit code: $exit_code)"
            ;;
        *)
            log_warning "OpenSCAP evaluation completed with exit code: $exit_code"
            ;;
    esac
    
    # Verify reports were generated and export results
    if [[ -f "$report_file" ]]; then
        local report_size=$(stat -c%s "$report_file")
        log_success "HTML report generated: $report_file"
        log "Report size: $report_size bytes"
        
        # Upload report to OpenSCAP Report Service (non-blocking)
        if upload_report_to_service "$report_file" "$report_size"; then
            log_success "Report uploaded to OpenSCAP Report Service"
        else
            log_warning "Failed to upload report to service, continuing with ConfigMap export"
        fi
        
        # Export results to ConfigMap
        export_results_to_configmap "$results_file" "$report_file" "$report_size"
    else
        log_error "HTML report was not generated"
        return 1
    fi
    
    if [[ -f "$results_file" ]]; then
        local results_size=$(stat -c%s "$results_file")
        log_success "XML results generated: $results_file"
        log "Results size: $results_size bytes"
    else
        log_error "XML results were not generated"
        return 1
    fi
    
    return $exit_code
}

# Upload HTML report to OpenSCAP Report Service
upload_report_to_service() {
    local report_file="$1"
    local report_size="$2"
    
    log "Uploading HTML report to OpenSCAP Report Service..."
    
    # Check if report service is configured
    local report_service_url="${OPENSCAP_REPORT_SERVICE_URL:-http://openscap-report-service.compliance-system.svc.cluster.local:8080}"
    
    if [[ ! -f "$report_file" ]]; then
        log_error "Report file not found: $report_file"
        return 1
    fi
    
    # Prepare form data
    local upload_url="${report_service_url}/upload"
    
    log "Uploading to: $upload_url"
    log "Report file: $report_file"
    log "Report size: $report_size bytes"
    
    # Upload using curl with form data
    local upload_response
    if upload_response=$(curl -s -X POST \
        -F "scanId=${SCAN_ID}" \
        -F "scanName=${SCAN_NAME}" \
        -F "nodeName=${NODE_NAME}" \
        -F "profile=${PROFILE}" \
        -F "scanner=openscap" \
        -F "report=@${report_file}" \
        "$upload_url" 2>&1); then
        
        log_success "Report uploaded successfully"
        log "Upload response: $upload_response"
        return 0
    else
        log_error "Failed to upload report: $upload_response"
        return 1
    fi
}

# Export OpenSCAP results to Kubernetes ConfigMap
export_results_to_configmap() {
    local results_file="$1"
    local report_file="$2"
    local report_size="$3"
    
    log "Exporting results to ConfigMap..."
    
    # Generate ConfigMap name based on job name
    local configmap_name="${JOB_NAME}-result"
    
    # Parse XML results to extract basic statistics
    local total_rules=0
    local pass_count=0
    local fail_count=0
    local error_count=0
    local notapplicable_count=0
    local manual_count=0
    
    if [[ -f "$results_file" ]]; then
        # Extract rule results from XML (simplified parsing)
        total_rules=$(grep -c '<rule-result' "$results_file" 2>/dev/null || echo "0")
        pass_count=$(grep -c 'result="pass"' "$results_file" 2>/dev/null || echo "0")
        fail_count=$(grep -c 'result="fail"' "$results_file" 2>/dev/null || echo "0")
        error_count=$(grep -c 'result="error"' "$results_file" 2>/dev/null || echo "0")
        notapplicable_count=$(grep -c 'result="notapplicable"' "$results_file" 2>/dev/null || echo "0")
        manual_count=$(grep -c 'result="needs_manual_review"' "$results_file" 2>/dev/null || echo "0")
    fi
    
    # Determine how to store the HTML report
    local html_storage_method="configmap"
    local html_content=""
    local html_metadata=""
    
    # ConfigMap size limit is ~1MB
    if [[ $report_size -gt 1000000 ]]; then
        log_warning "HTML report too large for ConfigMap ($report_size bytes), storing metadata only"
        html_storage_method="file"
        html_metadata="size=$report_size,path=$report_file,node=$NODE_NAME,scanId=$SCAN_ID"
    else
        log "HTML report fits in ConfigMap, including full content"
        if [[ -f "$report_file" ]]; then
            # Base64 encode HTML content to avoid YAML parsing issues
            html_content=$(base64 -w 0 "$report_file" 2>/dev/null || echo "")
        fi
    fi
    
    # Create ConfigMap YAML
    local configmap_yaml="/tmp/configmap.yaml"
    cat > "$configmap_yaml" << EOF
apiVersion: v1
kind: ConfigMap
metadata:
  name: ${configmap_name}
  namespace: ${NAMESPACE}
  labels:
    compliance-operator.alauda.io/scan: "${SCAN_NAME}"
    compliance-operator.alauda.io/scan-id: "${SCAN_ID}"
    compliance-operator.alauda.io/scanner: "openscap"
    compliance-operator.alauda.io/node: "${NODE_NAME}"
    compliance-operator.alauda.io/resource-type: "scan-result"
data:
  scan_result: |
    {
      "scanId": "${SCAN_ID}",
      "scanName": "${SCAN_NAME}",
      "nodeName": "${NODE_NAME}",
      "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
      "scanner": "openscap",
      "profile": "${PROFILE}",
      "content": "${CONTENT}",
      "statistics": {
        "total": ${total_rules},
        "pass": ${pass_count},
        "fail": ${fail_count},
        "error": ${error_count},
        "notapplicable": ${notapplicable_count},
        "manual": ${manual_count}
      },
      "htmlReport": {
        "storageMethod": "${html_storage_method}",
        "size": ${report_size},
        "metadata": "${html_metadata}"
      }
    }
EOF

    # Add HTML content if it fits in ConfigMap
    if [[ "$html_storage_method" == "configmap" && -n "$html_content" ]]; then
        cat >> "$configmap_yaml" << EOF
  html_report_base64: "${html_content}"
EOF
    fi

    # Add XML results (compressed to save space)
    if [[ -f "$results_file" ]]; then
        local compressed_xml=$(gzip -c "$results_file" | base64 -w 0 2>/dev/null || echo "")
        if [[ -n "$compressed_xml" ]]; then
            cat >> "$configmap_yaml" << EOF
  xml_results_gzip_base64: "${compressed_xml}"
EOF
        fi
    fi

    # Apply ConfigMap using kubectl
    if command -v kubectl >/dev/null 2>&1; then
        log "Creating ConfigMap with kubectl..."
        if kubectl apply -f "$configmap_yaml"; then
            log_success "ConfigMap created successfully: $configmap_name"
        else
            log_error "Failed to create ConfigMap with kubectl"
            return 1
        fi
    else
        log_error "kubectl not available, cannot create ConfigMap"
        log "ConfigMap YAML saved to: $configmap_yaml"
        return 1
    fi
    
    # Clean up temporary file
    rm -f "$configmap_yaml"
    
    return 0
}

# Main execution
main() {
    log "OpenSCAP Scanner starting..."
    log "Node: $NODE_NAME"
    log "Scan ID: $SCAN_ID"
    log "Profile: $PROFILE"
    log "Content: $CONTENT"
    log "Content Image: $CONTENT_IMAGE"
    log "Host Root: $HOSTROOT"
    log "Report Directory: $REPORT_DIR"
    
    # Validation steps
    validate_environment
    check_openscap
    setup_cpe_dict
    
    # Find content file
    local content_file
    content_file=$(check_content)

    # Setup offline scanning environment
    setup_offline_scanning_environment

    # Setup enhanced scanning environment
    setup_host_scanning_environment
    
    # List available profiles
    list_profiles "$content_file"
    
    # Run evaluation
    local scan_exit_code=0
    if run_evaluation "$content_file"; then
        log_success "OpenSCAP scan completed successfully"
    else
        scan_exit_code=$?
        if [[ $scan_exit_code -eq 2 ]]; then
            log_warning "OpenSCAP scan completed with findings"
            # Exit code 2 means scan was successful but found compliance issues
            # This should be considered a successful scan, not a failure
            log_success "Scan completed successfully - compliance findings are normal"
            scan_exit_code=0
        else
            log_error "OpenSCAP scan failed"
        fi
    fi
    
    log "OpenSCAP Scanner finished with exit code: $scan_exit_code"
    exit $scan_exit_code
}

# Execute main function
main "$@" 