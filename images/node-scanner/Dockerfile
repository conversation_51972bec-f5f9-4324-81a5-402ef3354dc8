# Node Scanner Image - 专用于节点级合规检查
# 包含系统工具，支持主机文件系统访问和特权操作

FROM alpine:3.18

# 安装必要的系统工具
RUN apk add --no-cache \
    bash \
    curl \
    jq \
    ca-certificates \
    coreutils \
    findutils \
    grep \
    sed \
    awk \
    procps \
    util-linux \
    shadow \
    openssl \
    && rm -rf /var/cache/apk/*

# 安装 kubectl（用于某些混合检查）
ARG KUBECTL_VERSION=v1.28.0
RUN curl -LO "https://dl.k8s.io/release/${KUBECTL_VERSION}/bin/linux/amd64/kubectl" \
    && chmod +x kubectl \
    && mv kubectl /usr/local/bin/

# 复制脚本
COPY scripts/node-scanner.sh /usr/local/bin/node-scanner.sh
RUN chmod +x /usr/local/bin/node-scanner.sh

# 创建结果目录
RUN mkdir -p /tmp/results

# 设置工作目录
WORKDIR /tmp

# 默认入口点
ENTRYPOINT ["/usr/local/bin/node-scanner.sh"]
