#!/bin/bash

# Node Scanner Script - 节点级合规检查
# 支持节点角色控制和主机文件系统访问

set -euo pipefail

# 环境变量
RULE_ID="${RULE_ID:-unknown}"
CHECK_TYPE="${CHECK_TYPE:-node}"
NODE_SCOPE="${NODE_SCOPE:-all}"
NODE_ROLE="${NODE_ROLE:-worker}"
SCAN_NAME="${SCAN_NAME:-unknown}"
NAMESPACE="${NAMESPACE:-default}"
JOB_NAME="${JOB_NAME:-unknown}"
SCAN_ID="${SCAN_ID:-unknown}"
NODE_NAME="${NODE_NAME:-unknown}"
RESULTS_DIR="${RESULTS_DIR:-/tmp/results}"
HOST_ROOT="${HOST_ROOT:-/host}"

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $*" >&2
}

log "Starting node scanner"
log "Rule ID: $RULE_ID"
log "Check Type: $CHECK_TYPE"
log "Node Scope: $NODE_SCOPE"
log "Node Role: $NODE_ROLE"
log "Node Name: $NODE_NAME"
log "Scan Name: $SCAN_NAME"
log "Job Name: $JOB_NAME"

# 确保结果目录存在
mkdir -p "$RESULTS_DIR"

# 结果文件
RESULT_FILE="$RESULTS_DIR/${RULE_ID}_${NODE_NAME}_result.json"
OUTPUT_FILE="$RESULTS_DIR/${RULE_ID}_${NODE_NAME}_output.txt"

# 验证主机文件系统挂载
if [ ! -d "$HOST_ROOT" ]; then
    log "ERROR: Host root directory not found: $HOST_ROOT"
    exit 1
fi

log "Host root directory verified: $HOST_ROOT"

# 验证节点角色匹配
validate_node_scope() {
    case "$NODE_SCOPE" in
        "all"|"")
            log "Node scope: all - executing on all nodes"
            return 0
            ;;
        "control-plane"|"master")
            if [[ "$NODE_ROLE" == "control-plane" || "$NODE_ROLE" == "master" ]]; then
                log "Node scope: $NODE_SCOPE - matches node role: $NODE_ROLE"
                return 0
            else
                log "Node scope: $NODE_SCOPE - does not match node role: $NODE_ROLE, skipping"
                return 1
            fi
            ;;
        "worker")
            if [[ "$NODE_ROLE" == "worker" ]]; then
                log "Node scope: $NODE_SCOPE - matches node role: $NODE_ROLE"
                return 0
            else
                log "Node scope: $NODE_SCOPE - does not match node role: $NODE_ROLE, skipping"
                return 1
            fi
            ;;
        *)
            log "Unknown node scope: $NODE_SCOPE"
            return 1
            ;;
    esac
}

# 验证节点范围
if ! validate_node_scope; then
    # 创建跳过结果
    cat > "$RESULT_FILE" << EOF
{
    "ruleId": "$RULE_ID",
    "checkType": "$CHECK_TYPE",
    "nodeScope": "$NODE_SCOPE",
    "nodeRole": "$NODE_ROLE",
    "nodeName": "$NODE_NAME",
    "status": "SKIP",
    "exitCode": 0,
    "output": "Rule skipped due to node scope mismatch",
    "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
    "scanId": "$SCAN_ID",
    "jobName": "$JOB_NAME",
    "scanName": "$SCAN_NAME",
    "namespace": "$NAMESPACE"
}
EOF
    log "Rule skipped, results saved to: $RESULT_FILE"
    exit 0
fi

# 执行节点级检查脚本
log "Executing node check script for rule: $RULE_ID"

# 创建临时脚本文件
TEMP_SCRIPT="/tmp/check_script.sh"
cat > "$TEMP_SCRIPT" << 'EOF'
#!/bin/bash
# 这里会被传入的检查脚本替换
EOF

# 将传入的检查脚本写入临时文件
if [ $# -gt 0 ]; then
    echo "$1" > "$TEMP_SCRIPT"
else
    log "ERROR: No check script provided"
    exit 1
fi

chmod +x "$TEMP_SCRIPT"

# 设置环境变量供检查脚本使用
export HOST_ROOT
export NODE_NAME
export NODE_ROLE
export NODE_SCOPE

# 执行检查脚本并捕获输出和退出码
EXIT_CODE=0
OUTPUT=""

log "Running node check script..."
if OUTPUT=$("$TEMP_SCRIPT" 2>&1); then
    EXIT_CODE=0
    log "Node check completed successfully"
else
    EXIT_CODE=$?
    log "Node check failed with exit code: $EXIT_CODE"
fi

# 保存输出到文件
echo "$OUTPUT" > "$OUTPUT_FILE"

# 确定检查状态
STATUS="PASS"
if [ $EXIT_CODE -ne 0 ]; then
    STATUS="FAIL"
fi

log "Check result: $STATUS (exit code: $EXIT_CODE)"

# 生成结果 JSON
cat > "$RESULT_FILE" << EOF
{
    "ruleId": "$RULE_ID",
    "checkType": "$CHECK_TYPE",
    "nodeScope": "$NODE_SCOPE",
    "nodeRole": "$NODE_ROLE",
    "nodeName": "$NODE_NAME",
    "status": "$STATUS",
    "exitCode": $EXIT_CODE,
    "output": $(echo "$OUTPUT" | jq -R -s .),
    "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
    "scanId": "$SCAN_ID",
    "jobName": "$JOB_NAME",
    "scanName": "$SCAN_NAME",
    "namespace": "$NAMESPACE"
}
EOF

log "Results saved to: $RESULT_FILE"

# 清理临时文件
rm -f "$TEMP_SCRIPT"

log "Node scanner completed"
exit $EXIT_CODE
