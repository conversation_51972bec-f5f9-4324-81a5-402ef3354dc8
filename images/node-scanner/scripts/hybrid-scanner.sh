#!/bin/bash

# Hybrid Scanner Script - 混合检查（API + 配置文件 + 进程）
# 专门用于 STIG 规则的复合检查需求

set -euo pipefail

# 环境变量
RULE_ID="${RULE_ID:-unknown}"
CHECK_TYPE="${CHECK_TYPE:-hybrid}"
NODE_SCOPE="${NODE_SCOPE:-all}"
NODE_ROLE="${NODE_ROLE:-worker}"
SCAN_NAME="${SCAN_NAME:-unknown}"
NAMESPACE="${NAMESPACE:-default}"
JOB_NAME="${JOB_NAME:-unknown}"
SCAN_ID="${SCAN_ID:-unknown}"
NODE_NAME="${NODE_NAME:-unknown}"
RESULTS_DIR="${RESULTS_DIR:-/tmp/results}"
HOST_ROOT="${HOST_ROOT:-/host}"

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [HYBRID] $*" >&2
}

log "Starting hybrid scanner for STIG mixed checks"
log "Rule ID: $RULE_ID"
log "Check Type: $CHECK_TYPE"
log "Node Scope: $NODE_SCOPE"
log "Node Role: $NODE_ROLE"
log "Node Name: $NODE_NAME"

# 确保结果目录存在
mkdir -p "$RESULTS_DIR"

# 结果文件
RESULT_FILE="$RESULTS_DIR/${RULE_ID}_${NODE_NAME}_result.json"
OUTPUT_FILE="$RESULTS_DIR/${RULE_ID}_${NODE_NAME}_output.txt"

# 验证环境
validate_environment() {
    log "Validating hybrid scanner environment..."
    
    # 检查主机文件系统挂载
    if [ ! -d "$HOST_ROOT" ]; then
        log "ERROR: Host root directory not found: $HOST_ROOT"
        return 1
    fi
    
    # 检查 kubectl 可用性
    if ! command -v kubectl &> /dev/null; then
        log "ERROR: kubectl not found"
        return 1
    fi
    
    # 检查 Kubernetes API 连接
    if ! kubectl cluster-info &> /dev/null; then
        log "ERROR: Cannot connect to Kubernetes API"
        return 1
    fi
    
    log "Environment validation passed"
    return 0
}

# 验证节点范围
validate_node_scope() {
    case "$NODE_SCOPE" in
        "all"|"")
            log "Node scope: all - executing on all nodes"
            return 0
            ;;
        "control-plane"|"master")
            if [[ "$NODE_ROLE" == "control-plane" || "$NODE_ROLE" == "master" ]]; then
                log "Node scope: $NODE_SCOPE - matches node role: $NODE_ROLE"
                return 0
            else
                log "Node scope: $NODE_SCOPE - does not match node role: $NODE_ROLE, skipping"
                return 1
            fi
            ;;
        "worker")
            if [[ "$NODE_ROLE" == "worker" ]]; then
                log "Node scope: $NODE_SCOPE - matches node role: $NODE_ROLE"
                return 0
            else
                log "Node scope: $NODE_SCOPE - does not match node role: $NODE_ROLE, skipping"
                return 1
            fi
            ;;
        *)
            log "Unknown node scope: $NODE_SCOPE"
            return 1
            ;;
    esac
}

# 设置混合检查环境
setup_hybrid_environment() {
    log "Setting up hybrid check environment..."
    
    # 导出环境变量供检查脚本使用
    export HOST_ROOT
    export NODE_NAME
    export NODE_ROLE
    export NODE_SCOPE
    export KUBECONFIG="/var/run/secrets/kubernetes.io/serviceaccount"
    
    # 创建便捷函数供检查脚本使用
    cat > /tmp/hybrid_functions.sh << 'EOF'
#!/bin/bash

# 便捷函数：检查文件是否存在
check_host_file() {
    local file_path="$1"
    local host_file="${HOST_ROOT}${file_path}"
    [ -f "$host_file" ]
}

# 便捷函数：读取主机文件内容
read_host_file() {
    local file_path="$1"
    local host_file="${HOST_ROOT}${file_path}"
    if [ -f "$host_file" ]; then
        cat "$host_file"
    else
        return 1
    fi
}

# 便捷函数：检查主机进程
check_host_process() {
    local process_name="$1"
    chroot "$HOST_ROOT" pgrep -f "$process_name" > /dev/null
}

# 便捷函数：获取主机进程信息
get_host_process_info() {
    local process_name="$1"
    chroot "$HOST_ROOT" ps aux | grep "$process_name" | grep -v grep
}

# 便捷函数：检查主机端口
check_host_port() {
    local port="$1"
    chroot "$HOST_ROOT" netstat -tlnp | grep ":$port " > /dev/null
}

# 便捷函数：Kubernetes API 检查
k8s_check() {
    kubectl "$@"
}

# 便捷函数：检查节点标签
check_node_label() {
    local label_key="$1"
    local expected_value="$2"
    local actual_value=$(kubectl get node "$NODE_NAME" -o jsonpath="{.metadata.labels['$label_key']}")
    [ "$actual_value" = "$expected_value" ]
}

# 便捷函数：检查节点注解
check_node_annotation() {
    local annotation_key="$1"
    local expected_value="$2"
    local actual_value=$(kubectl get node "$NODE_NAME" -o jsonpath="{.metadata.annotations['$annotation_key']}")
    [ "$actual_value" = "$expected_value" ]
}
EOF

    source /tmp/hybrid_functions.sh
    log "Hybrid environment setup completed"
}

# 主执行逻辑
main() {
    # 验证环境
    if ! validate_environment; then
        exit 1
    fi
    
    # 验证节点范围
    if ! validate_node_scope; then
        # 创建跳过结果
        cat > "$RESULT_FILE" << EOF
{
    "ruleId": "$RULE_ID",
    "checkType": "$CHECK_TYPE",
    "nodeScope": "$NODE_SCOPE",
    "nodeRole": "$NODE_ROLE",
    "nodeName": "$NODE_NAME",
    "status": "SKIP",
    "exitCode": 0,
    "output": "Rule skipped due to node scope mismatch",
    "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
    "scanId": "$SCAN_ID",
    "jobName": "$JOB_NAME",
    "scanName": "$SCAN_NAME",
    "namespace": "$NAMESPACE"
}
EOF
        log "Rule skipped, results saved to: $RESULT_FILE"
        exit 0
    fi
    
    # 设置混合检查环境
    setup_hybrid_environment
    
    # 执行混合检查脚本
    log "Executing hybrid check script for rule: $RULE_ID"
    
    # 创建临时脚本文件
    TEMP_SCRIPT="/tmp/check_script.sh"
    cat > "$TEMP_SCRIPT" << 'EOF'
#!/bin/bash
# 这里会被传入的检查脚本替换
EOF
    
    # 将传入的检查脚本写入临时文件
    if [ $# -gt 0 ]; then
        echo "$1" > "$TEMP_SCRIPT"
    else
        log "ERROR: No check script provided"
        exit 1
    fi
    
    chmod +x "$TEMP_SCRIPT"
    
    # 在脚本开头添加便捷函数导入
    sed -i '1a source /tmp/hybrid_functions.sh' "$TEMP_SCRIPT"
    
    # 执行检查脚本并捕获输出和退出码
    EXIT_CODE=0
    OUTPUT=""
    
    log "Running hybrid check script..."
    if OUTPUT=$("$TEMP_SCRIPT" 2>&1); then
        EXIT_CODE=0
        log "Hybrid check completed successfully"
    else
        EXIT_CODE=$?
        log "Hybrid check failed with exit code: $EXIT_CODE"
    fi
    
    # 保存输出到文件
    echo "$OUTPUT" > "$OUTPUT_FILE"
    
    # 确定检查状态
    STATUS="PASS"
    if [ $EXIT_CODE -ne 0 ]; then
        STATUS="FAIL"
    fi
    
    log "Check result: $STATUS (exit code: $EXIT_CODE)"
    
    # 生成结果 JSON
    cat > "$RESULT_FILE" << EOF
{
    "ruleId": "$RULE_ID",
    "checkType": "$CHECK_TYPE",
    "nodeScope": "$NODE_SCOPE",
    "nodeRole": "$NODE_ROLE",
    "nodeName": "$NODE_NAME",
    "status": "$STATUS",
    "exitCode": $EXIT_CODE,
    "output": $(echo "$OUTPUT" | jq -R -s .),
    "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
    "scanId": "$SCAN_ID",
    "jobName": "$JOB_NAME",
    "scanName": "$SCAN_NAME",
    "namespace": "$NAMESPACE",
    "capabilities": ["api", "filesystem", "process"]
}
EOF
    
    log "Results saved to: $RESULT_FILE"
    
    # 清理临时文件
    rm -f "$TEMP_SCRIPT" "/tmp/hybrid_functions.sh"
    
    log "Hybrid scanner completed"
    exit $EXIT_CODE
}

# 执行主函数
main "$@"
