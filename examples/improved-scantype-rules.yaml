# 改进后的 ScanType 规则示例
# 展示正确的 Platform 和 Node 扫描区别

---
# 示例1: 真正的平台级检查 - RBAC 配置
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-rbac-anonymous-access
  namespace: compliance-system
spec:
  id: "V-242417"
  title: "Kubernetes must prevent anonymous access to cluster resources"
  checkText: "Review cluster RBAC policies to ensure anonymous access is properly restricted"
  severity: "high"
  checkType: "platform"  # 平台级检查 - 通过 K8s API
  checkScript: |
    #!/bin/bash
    # 检查匿名用户权限（平台级检查）
    echo "Checking anonymous user permissions..."
    
    # 检查匿名用户是否有过多权限
    ANONYMOUS_PERMS=$(kubectl auth can-i --list --as=system:anonymous 2>/dev/null | grep -v "No resources found" | wc -l)
    
    if [ "$ANONYMOUS_PERMS" -eq 0 ]; then
        echo "PASS: Anonymous access properly restricted"
        exit 0
    else
        echo "FAIL: Anonymous user has $ANONYMOUS_PERMS permissions"
        kubectl auth can-i --list --as=system:anonymous
        exit 1
    fi

---
# 示例2: 真正的平台级检查 - NetworkPolicy
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-network-policies
  namespace: compliance-system
spec:
  id: "V-242418"
  title: "Kubernetes must implement network segmentation"
  checkText: "Verify that NetworkPolicies are configured to restrict pod-to-pod communication"
  severity: "medium"
  checkType: "platform"  # 平台级检查 - 通过 K8s API
  checkScript: |
    #!/bin/bash
    # 检查 NetworkPolicy 配置（平台级检查）
    echo "Checking NetworkPolicy configuration..."
    
    # 检查是否有 NetworkPolicy 配置
    NETPOL_COUNT=$(kubectl get networkpolicies --all-namespaces --no-headers | wc -l)
    
    if [ "$NETPOL_COUNT" -gt 0 ]; then
        echo "PASS: Found $NETPOL_COUNT NetworkPolicies configured"
        kubectl get networkpolicies --all-namespaces
        exit 0
    else
        echo "FAIL: No NetworkPolicies found - network segmentation not implemented"
        exit 1
    fi

---
# 示例3: Control Plane 节点级检查 - admin.conf 权限
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-admin-conf-permissions
  namespace: compliance-system
spec:
  id: "V-242404"
  title: "The Kubernetes admin.conf must have proper file permissions"
  checkText: "On the Control Plane, verify admin.conf file permissions are 600 or more restrictive"
  severity: "high"
  checkType: "node"           # 节点级检查 - 需要访问文件系统
  nodeScope: "control-plane"  # 只在 Control Plane 节点执行
  checkScript: |
    #!/bin/bash
    # 检查 admin.conf 文件权限（Control Plane 节点级检查）
    echo "Checking admin.conf file permissions on Control Plane..."
    
    ADMIN_CONF="/host/etc/kubernetes/admin.conf"
    
    if [ -f "$ADMIN_CONF" ]; then
        PERMS=$(stat -c "%a" "$ADMIN_CONF")
        OWNER=$(stat -c "%U:%G" "$ADMIN_CONF")
        
        echo "admin.conf permissions: $PERMS"
        echo "admin.conf owner: $OWNER"
        
        if [ "$PERMS" = "600" ] && [ "$OWNER" = "root:root" ]; then
            echo "PASS: admin.conf has correct permissions (600) and ownership (root:root)"
            exit 0
        else
            echo "FAIL: admin.conf permissions ($PERMS) or ownership ($OWNER) incorrect"
            exit 1
        fi
    else
        echo "FAIL: admin.conf not found at $ADMIN_CONF"
        exit 1
    fi

---
# 示例4: Worker 节点级检查 - kubelet 配置
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-worker-kubelet-anonymous
  namespace: compliance-system
spec:
  id: "V-242407"
  title: "Worker node kubelet must disable anonymous authentication"
  checkText: "On Worker Nodes, verify kubelet anonymous authentication is disabled"
  severity: "medium"
  checkType: "node"     # 节点级检查 - 需要访问文件系统
  nodeScope: "worker"   # 只在 Worker 节点执行
  checkScript: |
    #!/bin/bash
    # 检查 Worker 节点 kubelet 匿名认证配置
    echo "Checking kubelet anonymous authentication on Worker node..."
    
    # 检查 kubelet 配置文件
    KUBELET_CONFIG="/host/var/lib/kubelet/config.yaml"
    
    if [ -f "$KUBELET_CONFIG" ]; then
        # 检查是否禁用了匿名认证
        if grep -q "anonymous:" "$KUBELET_CONFIG"; then
            ANONYMOUS_ENABLED=$(grep -A1 "anonymous:" "$KUBELET_CONFIG" | grep "enabled:" | awk '{print $2}')
            if [ "$ANONYMOUS_ENABLED" = "false" ]; then
                echo "PASS: Kubelet anonymous authentication is disabled"
                exit 0
            else
                echo "FAIL: Kubelet anonymous authentication is enabled"
                exit 1
            fi
        else
            echo "FAIL: Anonymous authentication configuration not found in kubelet config"
            exit 1
        fi
    else
        echo "FAIL: Kubelet config file not found at $KUBELET_CONFIG"
        exit 1
    fi

---
# 示例5: 所有节点级检查 - 文件系统权限
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-kubelet-service-file
  namespace: compliance-system
spec:
  id: "V-242408"
  title: "Kubelet service file must have proper permissions"
  checkText: "On Control Plane and Worker Nodes, verify kubelet service file permissions"
  severity: "medium"
  checkType: "node"  # 节点级检查 - 需要访问文件系统
  nodeScope: "all"   # 在所有节点执行
  checkScript: |
    #!/bin/bash
    # 检查所有节点的 kubelet 服务文件权限
    echo "Checking kubelet service file permissions on all nodes..."
    
    # 可能的 kubelet 服务文件位置
    SERVICE_FILES=(
        "/host/etc/systemd/system/kubelet.service"
        "/host/lib/systemd/system/kubelet.service"
        "/host/usr/lib/systemd/system/kubelet.service"
    )
    
    FOUND=false
    for SERVICE_FILE in "${SERVICE_FILES[@]}"; do
        if [ -f "$SERVICE_FILE" ]; then
            FOUND=true
            PERMS=$(stat -c "%a" "$SERVICE_FILE")
            OWNER=$(stat -c "%U:%G" "$SERVICE_FILE")
            
            echo "Found kubelet service file: $SERVICE_FILE"
            echo "Permissions: $PERMS"
            echo "Owner: $OWNER"
            
            if [ "$PERMS" = "644" ] && [ "$OWNER" = "root:root" ]; then
                echo "PASS: Kubelet service file has correct permissions"
                exit 0
            else
                echo "FAIL: Kubelet service file permissions or ownership incorrect"
                exit 1
            fi
        fi
    done
    
    if [ "$FOUND" = false ]; then
        echo "FAIL: Kubelet service file not found in any expected location"
        exit 1
    fi

---
# 示例6: 扫描配置 - 使用改进的扫描类型
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Scan
metadata:
  name: improved-stig-scan
  namespace: compliance-system
spec:
  profile: stig-k8s-improved
  scanType: "both"  # 执行平台级和节点级检查
  
  # 自动策略：根据规则的 checkType 和 nodeScope 自动创建相应的 Job
  nodeScopeStrategy: "auto"
  
  # 这个扫描会自动：
  # 1. 为 checkType="platform" 的规则创建平台级 Job（使用 platform-scanner 镜像）
  # 2. 为 checkType="node" + nodeScope="control-plane" 的规则在 master 节点创建 Job
  # 3. 为 checkType="node" + nodeScope="worker" 的规则在 worker 节点创建 Job  
  # 4. 为 checkType="node" + nodeScope="all" 的规则在所有节点创建 Job
