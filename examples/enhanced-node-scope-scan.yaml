---
# Enhanced Scan with automatic node scope handling
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Scan
metadata:
  name: stig-k8s-enhanced-node-scan
  namespace: compliance-system
  labels:
    compliance-operator.alauda.io/scan-type: node
    compliance-operator.alauda.io/node-scope: auto
spec:
  profile: stig-k8s-v2r2-enhanced
  scanType: "node"
  
  # Auto strategy: automatically create separate jobs based on rule node scopes
  nodeScopeStrategy: "auto"
  
  # Optional: specify which node roles to include in the scan
  targetNodeRoles:
    - "control-plane"
    - "worker"
  
  # Optional: additional node selector criteria
  nodeSelector:
    kubernetes.io/os: "linux"

---
# Manual node scope control - Control Plane only
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Scan
metadata:
  name: stig-k8s-control-plane-only-scan
  namespace: compliance-system
spec:
  profile: stig-k8s-v2r2-enhanced
  scanType: "node"
  
  # Manual strategy: use only the specified nodeSelector
  nodeScopeStrategy: "manual"
  
  # Target only control plane nodes
  nodeSelector:
    node-role.kubernetes.io/control-plane: ""

---
# Manual node scope control - Worker nodes only
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Scan
metadata:
  name: stig-k8s-worker-only-scan
  namespace: compliance-system
spec:
  profile: stig-k8s-v2r2-enhanced
  scanType: "node"
  
  # Manual strategy: use only the specified nodeSelector
  nodeScopeStrategy: "manual"
  
  # Target only worker nodes
  nodeSelector:
    node-role.kubernetes.io/worker: ""

---
# Enhanced ComplianceSuite with node scope awareness
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: ComplianceSuite
metadata:
  name: stig-k8s-enhanced-suite
  namespace: compliance-system
spec:
  schedule: "0 2 * * 1"  # Weekly on Monday at 2 AM
  
  scans:
    # Platform scan (unchanged)
    - name: "platform-security"
      profile: stig-k8s-v2r2-platform
      scanType: "platform"
    
    # Enhanced node scan with automatic node scope handling
    - name: "node-security-auto"
      profile: stig-k8s-v2r2-enhanced
      scanType: "node"
      # Auto strategy will create separate jobs for different node scopes
      # This single scan definition will automatically:
      # 1. Create jobs for control-plane nodes (for rules with nodeScope: "control-plane")
      # 2. Create jobs for worker nodes (for rules with nodeScope: "worker") 
      # 3. Create jobs for all nodes (for rules with nodeScope: "all")
    
    # Specific control plane security scan
    - name: "control-plane-specific"
      profile: stig-k8s-v2r2-control-plane
      scanType: "node"
      nodeSelector:
        node-role.kubernetes.io/control-plane: ""
    
    # Specific worker node security scan  
    - name: "worker-specific"
      profile: stig-k8s-v2r2-worker
      scanType: "node"
      nodeSelector:
        node-role.kubernetes.io/worker: ""
