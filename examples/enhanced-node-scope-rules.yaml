---
# Example: Rule that applies only to Control Plane nodes
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-admin-conf-file-ownership-enhanced
  namespace: compliance-system
spec:
  id: "V-242404"
  title: "The Kubernetes admin.conf must be owned by root."
  description: |
    The admin.conf is the administrator kubeconfig file defining administrator context for the cluster. 
    This file contains the key and certificate for the root user of the cluster. 
    If an attacker can gain access to this file, changes can be made to open vulnerabilities 
    and bypass user authorizations inherent within Kubernetes with RBAC implemented.
  checkText: |
    On the Control Plane, run the command:
    stat -c %U:%G /etc/kubernetes/admin.conf
    
    If the file is not owned by root:root, this is a finding.
  fixText: |
    On the Control Plane, run the command:
    chown root:root /etc/kubernetes/admin.conf
  severity: "high"
  checkType: "node"
  nodeScope: "control-plane"  # Only applies to control plane nodes
  checkScript: |
    #!/bin/bash
    echo "Checking admin.conf file ownership on Control Plane..."
    
    ADMIN_CONF_PATHS="/host/etc/kubernetes/admin.conf"
    
    for conf_path in $ADMIN_CONF_PATHS; do
      if [ -f "$conf_path" ]; then
        owner=$(stat -c "%U:%G" "$conf_path" 2>/dev/null)
        echo "✓ Found admin.conf: $conf_path"
        echo "  Owner: $owner"
        
        if [ "$owner" = "root:root" ]; then
          echo "PASS: admin.conf is owned by root:root"
          exit 0
        else
          echo "FAIL: admin.conf is not owned by root:root (current: $owner)"
          exit 1
        fi
      fi
    done
    
    echo "FAIL: admin.conf not found"
    exit 1

---
# Example: Rule that applies to both Control Plane and Worker nodes
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-kubelet-config-permissions-enhanced
  namespace: compliance-system
spec:
  id: "V-242407"
  title: "The Kubernetes KubeletConfiguration files must have file permissions set to 644 or more restrictive."
  description: |
    The kubelet configuration file contains the runtime configuration of the kubelet service. 
    If an attacker can gain access to this file, changes can be made to open vulnerabilities 
    and bypass user authorizations inherit within Kubernetes with RBAC implemented.
  checkText: |
    On the Control Plane and Worker Nodes, run the command:
    ps -ef | grep kubelet
    
    Check the config file (path identified by: --config):
    stat -c %a /path/to/kubelet/config
    
    If the permissions are not 644 or more restrictive, this is a finding.
  fixText: |
    On the Control Plane and Worker Nodes, run the command:
    chmod 644 /path/to/kubelet/config
  severity: "medium"
  checkType: "node"
  nodeScope: "all"  # Applies to all nodes (both control-plane and worker)
  checkScript: |
    #!/bin/bash
    echo "Checking kubelet configuration file permissions..."
    
    # Find kubelet config file from running process
    if [ -d "/host/proc" ]; then
      for pid in $(ls /host/proc | grep '^[0-9]*$'); do
        if [ -f "/host/proc/$pid/cmdline" ] && grep -q kubelet "/host/proc/$pid/cmdline" 2>/dev/null; then
          cmdline=$(tr '\0' ' ' < "/host/proc/$pid/cmdline")
          if echo "$cmdline" | grep -q -- "--config"; then
            config_file=$(echo "$cmdline" | sed 's/.*--config[= ]\([^ ]*\).*/\1/')
            if [ -f "/host$config_file" ]; then
              perms=$(stat -c "%a" "/host$config_file" 2>/dev/null)
              echo "✓ Found kubelet config: /host$config_file"
              echo "  Permissions: $perms"
              
              # Check if permissions are 644 or more restrictive
              if [ "$perms" -le 644 ]; then
                echo "PASS: kubelet config has appropriate permissions ($perms)"
                exit 0
              else
                echo "FAIL: kubelet config permissions too permissive ($perms, should be 644 or less)"
                exit 1
              fi
            fi
          fi
        fi
      done
    fi
    
    echo "FAIL: kubelet config file not found"
    exit 1

---
# Example: Rule that applies only to Worker nodes
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-worker-node-security-enhanced
  namespace: compliance-system
spec:
  id: "V-242420"
  title: "Worker nodes must have specific security configurations."
  description: |
    Worker nodes run the actual workloads and must have specific security configurations
    that differ from control plane nodes.
  checkText: |
    On Worker Nodes, verify specific worker node security settings.
  fixText: |
    On Worker Nodes, apply the required security configurations.
  severity: "medium"
  checkType: "node"
  nodeScope: "worker"  # Only applies to worker nodes
  nodeSelector:  # Additional selector for specific worker node requirements
    kubernetes.io/os: "linux"
  checkScript: |
    #!/bin/bash
    echo "Checking worker node specific security configurations..."
    
    # Example worker-specific checks
    echo "PASS: Worker node security check completed"
    exit 0
