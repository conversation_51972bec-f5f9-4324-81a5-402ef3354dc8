# ScanType vs NodeScope 概念说明和示例

---
# 示例1: Control Plane 节点的节点级检查
# ScanType: node (需要访问文件系统)
# NodeScope: control-plane (只在 master 节点执行)
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-admin-conf-ownership
  namespace: compliance-system
spec:
  id: "V-242404"
  title: "The Kubernetes admin.conf must be owned by root."
  checkText: "On the Control Plane, run: stat -c %U:%G /etc/kubernetes/admin.conf"
  severity: "high"
  checkType: "node"        # 节点级检查（需要访问文件系统）
  nodeScope: "control-plane"  # 只在 Control Plane 节点执行
  checkScript: |
    #!/bin/bash
    # 检查 admin.conf 文件所有权（只在 Control Plane 节点）
    if [ -f "/host/etc/kubernetes/admin.conf" ]; then
      owner=$(stat -c "%U:%G" "/host/etc/kubernetes/admin.conf")
      if [ "$owner" = "root:root" ]; then
        echo "PASS: admin.conf owned by root:root"
        exit 0
      else
        echo "FAIL: admin.conf not owned by root:root"
        exit 1
      fi
    fi
    echo "FAIL: admin.conf not found"
    exit 1

---
# 示例2: Worker 节点的节点级检查
# ScanType: node (需要访问文件系统)
# NodeScope: worker (只在 worker 节点执行)
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-worker-kubelet-config
  namespace: compliance-system
spec:
  id: "V-242407"
  title: "Worker node kubelet configuration must be secure."
  checkText: "On Worker Nodes, verify kubelet configuration security."
  severity: "medium"
  checkType: "node"     # 节点级检查（需要访问文件系统）
  nodeScope: "worker"   # 只在 Worker 节点执行
  checkScript: |
    #!/bin/bash
    # Worker 节点特定的 kubelet 配置检查
    echo "Checking worker node kubelet configuration..."
    echo "PASS: Worker kubelet config check completed"
    exit 0

---
# 示例3: 所有节点的节点级检查
# ScanType: node (需要访问文件系统)
# NodeScope: all (在所有节点执行)
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-kubelet-permissions
  namespace: compliance-system
spec:
  id: "V-242408"
  title: "Kubelet configuration files must have proper permissions."
  checkText: "On Control Plane and Worker Nodes, check kubelet config permissions."
  severity: "medium"
  checkType: "node"  # 节点级检查（需要访问文件系统）
  nodeScope: "all"   # 在所有节点执行
  checkScript: |
    #!/bin/bash
    # 检查所有节点的 kubelet 配置文件权限
    echo "Checking kubelet config permissions on all nodes..."
    echo "PASS: Kubelet permissions check completed"
    exit 0

---
# 示例4: 集群级平台检查（与节点角色无关）
# ScanType: platform (通过 Kubernetes API)
# NodeScope: 不适用（平台检查不涉及特定节点）
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-rbac-policies
  namespace: compliance-system
spec:
  id: "V-242417"
  title: "Kubernetes RBAC policies must be properly configured."
  checkText: "Review cluster RBAC policies for proper access control."
  severity: "high"
  checkType: "platform"  # 平台级检查（通过 K8s API）
  # nodeScope: 不需要，因为是平台级检查
  checkScript: |
    #!/bin/bash
    # 检查集群级 RBAC 配置
    kubectl get clusterroles --no-headers | wc -l
    echo "PASS: RBAC policies reviewed"
    exit 0

---
# 示例5: 扫描配置 - 自动处理不同节点范围
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Scan
metadata:
  name: comprehensive-stig-scan
  namespace: compliance-system
spec:
  profile: stig-k8s-comprehensive
  scanType: "both"  # 执行平台级和节点级检查
  
  # 自动策略：根据规则的 nodeScope 自动创建相应的 Job
  nodeScopeStrategy: "auto"
  
  # 这个扫描会自动：
  # 1. 为 checkType="platform" 的规则创建平台级 Job
  # 2. 为 checkType="node" + nodeScope="control-plane" 的规则在 master 节点创建 Job
  # 3. 为 checkType="node" + nodeScope="worker" 的规则在 worker 节点创建 Job  
  # 4. 为 checkType="node" + nodeScope="all" 的规则在所有节点创建 Job
