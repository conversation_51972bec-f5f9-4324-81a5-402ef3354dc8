// 控制器中处理 ScanType 和 NodeScope 的伪代码逻辑

func (r *ScanReconciler) createNodeScanJobs(ctx context.Context, scan *Scan, rules []Rule, scanID string) error {
    // 获取所有节点
    var allNodes []corev1.Node
    if err := r.List(ctx, &allNodes, client.MatchingLabels(scan.Spec.NodeSelector)); err != nil {
        return err
    }

    // 根据 NodeScope 分组规则
    ruleGroups := r.groupRulesByNodeScope(rules)
    
    for nodeScope, scopedRules := range ruleGroups {
        // 根据 nodeScope 筛选目标节点
        targetNodes := r.filterNodesByScope(allNodes, nodeScope)
        
        // 为每个目标节点创建扫描 Job
        for _, node := range targetNodes {
            for _, rule := range scopedRules {
                jobName := generateJobName("node", rule.Name, node.Name)
                
                job := &batchv1.Job{
                    ObjectMeta: metav1.ObjectMeta{
                        Name:      jobName,
                        Namespace: scan.Namespace,
                        Labels: map[string]string{
                            "compliance-operator.alauda.io/scan":      scan.Name,
                            "compliance-operator.alauda.io/rule":      rule.Name,
                            "compliance-operator.alauda.io/node":      node.Name,
                            "compliance-operator.alauda.io/node-role": r.getNodeRole(node),
                            "compliance-operator.alauda.io/node-scope": nodeScope,
                            "compliance-operator.alauda.io/scan-type": "node",
                        },
                    },
                    Spec: batchv1.JobSpec{
                        Template: corev1.PodTemplateSpec{
                            Spec: corev1.PodSpec{
                                NodeSelector: map[string]string{
                                    "kubernetes.io/hostname": node.Name,
                                },
                                // ... 其他 Pod 配置
                            },
                        },
                    },
                }
                
                if err := r.Create(ctx, job); err != nil {
                    return err
                }
            }
        }
    }
    
    return nil
}

// 根据 NodeScope 分组规则
func (r *ScanReconciler) groupRulesByNodeScope(rules []Rule) map[string][]Rule {
    groups := make(map[string][]Rule)
    
    for _, rule := range rules {
        if rule.Spec.CheckType != "node" {
            continue // 只处理节点级规则
        }
        
        nodeScope := rule.Spec.NodeScope
        if nodeScope == "" {
            nodeScope = "all" // 默认为所有节点
        }
        
        groups[nodeScope] = append(groups[nodeScope], rule)
    }
    
    return groups
}

// 根据 NodeScope 筛选节点
func (r *ScanReconciler) filterNodesByScope(allNodes []corev1.Node, nodeScope string) []corev1.Node {
    var targetNodes []corev1.Node
    
    for _, node := range allNodes {
        nodeRole := r.getNodeRole(node)
        
        switch nodeScope {
        case "all":
            targetNodes = append(targetNodes, node)
        case "control-plane", "master":
            if nodeRole == "control-plane" || nodeRole == "master" {
                targetNodes = append(targetNodes, node)
            }
        case "worker":
            if nodeRole == "worker" {
                targetNodes = append(targetNodes, node)
            }
        }
    }
    
    return targetNodes
}

// 获取节点角色
func (r *ScanReconciler) getNodeRole(node corev1.Node) string {
    if _, exists := node.Labels["node-role.kubernetes.io/control-plane"]; exists {
        return "control-plane"
    }
    if _, exists := node.Labels["node-role.kubernetes.io/master"]; exists {
        return "master"
    }
    return "worker"
}

// 处理自动节点范围策略
func (r *ScanReconciler) handleAutoNodeScopeStrategy(ctx context.Context, scan *Scan, profile *Profile, scanID string) error {
    // 获取 Profile 中的所有规则
    rules := r.getRulesFromProfile(profile)
    
    // 分离平台级和节点级规则
    var platformRules, nodeRules []Rule
    for _, rule := range rules {
        if rule.Spec.CheckType == "platform" {
            platformRules = append(platformRules, rule)
        } else if rule.Spec.CheckType == "node" {
            nodeRules = append(nodeRules, rule)
        }
    }
    
    // 创建平台级扫描 Job
    if len(platformRules) > 0 && (scan.Spec.ScanType == "platform" || scan.Spec.ScanType == "both") {
        if err := r.createPlatformScanJobs(ctx, scan, platformRules, scanID); err != nil {
            return err
        }
    }
    
    // 创建节点级扫描 Job（根据 NodeScope 自动分组）
    if len(nodeRules) > 0 && (scan.Spec.ScanType == "node" || scan.Spec.ScanType == "both") {
        if err := r.createNodeScanJobs(ctx, scan, nodeRules, scanID); err != nil {
            return err
        }
    }
    
    return nil
}
