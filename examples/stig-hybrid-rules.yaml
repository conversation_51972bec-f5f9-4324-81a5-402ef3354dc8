# STIG 混合检查规则示例
# 展示需要 API + 配置文件 + 进程检查的复杂 STIG 规则

---
# 示例1: kubelet 配置和 API 状态混合检查
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-kubelet-config-and-status
  namespace: compliance-system
spec:
  id: "V-242415"
  title: "Kubelet configuration must be properly secured and nodes must be ready"
  checkText: "Verify kubelet configuration files are secure AND nodes are in Ready state"
  severity: "high"
  checkType: "hybrid"  # 混合检查：配置文件 + API 状态
  nodeScope: "all"
  checkScript: |
    #!/bin/bash
    # STIG 混合检查：kubelet 配置 + 节点状态
    
    echo "=== STIG Hybrid Check: Kubelet Config + Node Status ==="
    
    # 第一部分：检查 kubelet 配置文件（文件系统检查）
    echo "1. Checking kubelet configuration file..."
    KUBELET_CONFIG="/etc/kubernetes/kubelet/kubelet-config.json"
    
    if check_host_file "$KUBELET_CONFIG"; then
        echo "✓ Kubelet config file exists"
        
        # 检查文件权限
        CONFIG_PERMS=$(stat -c "%a" "${HOST_ROOT}${KUBELET_CONFIG}")
        if [ "$CONFIG_PERMS" = "600" ]; then
            echo "✓ Kubelet config has correct permissions (600)"
        else
            echo "✗ Kubelet config permissions incorrect: $CONFIG_PERMS"
            exit 1
        fi
        
        # 检查配置内容
        CONFIG_CONTENT=$(read_host_file "$KUBELET_CONFIG")
        if echo "$CONFIG_CONTENT" | grep -q '"readOnlyPort": 0'; then
            echo "✓ Kubelet read-only port is disabled"
        else
            echo "✗ Kubelet read-only port not properly disabled"
            exit 1
        fi
    else
        echo "✗ Kubelet config file not found"
        exit 1
    fi
    
    # 第二部分：检查 kubelet 进程（进程检查）
    echo "2. Checking kubelet process..."
    if check_host_process "kubelet"; then
        echo "✓ Kubelet process is running"
        
        # 获取进程详细信息
        KUBELET_PROC=$(get_host_process_info "kubelet")
        echo "Kubelet process info: $KUBELET_PROC"
        
        # 检查是否使用了正确的配置文件
        if echo "$KUBELET_PROC" | grep -q -- "--config=$KUBELET_CONFIG"; then
            echo "✓ Kubelet using correct config file"
        else
            echo "✗ Kubelet not using expected config file"
            exit 1
        fi
    else
        echo "✗ Kubelet process not running"
        exit 1
    fi
    
    # 第三部分：检查节点 API 状态（API 检查）
    echo "3. Checking node status via Kubernetes API..."
    NODE_STATUS=$(k8s_check get node "$NODE_NAME" -o jsonpath='{.status.conditions[?(@.type=="Ready")].status}')
    
    if [ "$NODE_STATUS" = "True" ]; then
        echo "✓ Node is in Ready state"
    else
        echo "✗ Node is not Ready: $NODE_STATUS"
        exit 1
    fi
    
    # 检查节点标签
    if check_node_label "node.kubernetes.io/instance-type" ""; then
        echo "✓ Node has proper instance type label"
    fi
    
    echo "=== All checks passed ==="
    exit 0

---
# 示例2: API Server 配置和网络策略混合检查
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-apiserver-network-policy
  namespace: compliance-system
spec:
  id: "V-242420"
  title: "API Server must be properly configured and network policies must be enforced"
  checkText: "Verify API Server configuration AND network policies are properly implemented"
  severity: "high"
  checkType: "hybrid"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    # STIG 混合检查：API Server 配置 + 网络策略
    
    echo "=== STIG Hybrid Check: API Server + Network Policies ==="
    
    # 第一部分：检查 API Server 配置文件（文件系统检查）
    echo "1. Checking API Server configuration..."
    API_SERVER_MANIFEST="/etc/kubernetes/manifests/kube-apiserver.yaml"
    
    if check_host_file "$API_SERVER_MANIFEST"; then
        echo "✓ API Server manifest exists"
        
        MANIFEST_CONTENT=$(read_host_file "$API_SERVER_MANIFEST")
        
        # 检查关键安全配置
        if echo "$MANIFEST_CONTENT" | grep -q -- "--anonymous-auth=false"; then
            echo "✓ Anonymous authentication disabled"
        else
            echo "✗ Anonymous authentication not properly disabled"
            exit 1
        fi
        
        if echo "$MANIFEST_CONTENT" | grep -q -- "--authorization-mode.*RBAC"; then
            echo "✓ RBAC authorization enabled"
        else
            echo "✗ RBAC authorization not properly configured"
            exit 1
        fi
    else
        echo "✗ API Server manifest not found"
        exit 1
    fi
    
    # 第二部分：检查 API Server 进程（进程检查）
    echo "2. Checking API Server process..."
    if check_host_process "kube-apiserver"; then
        echo "✓ API Server process is running"
        
        # 检查 API Server 监听端口
        if check_host_port "6443"; then
            echo "✓ API Server listening on secure port 6443"
        else
            echo "✗ API Server not listening on expected port"
            exit 1
        fi
    else
        echo "✗ API Server process not running"
        exit 1
    fi
    
    # 第三部分：检查网络策略（API 检查）
    echo "3. Checking Network Policies via Kubernetes API..."
    
    # 检查是否有网络策略
    NETPOL_COUNT=$(k8s_check get networkpolicies --all-namespaces --no-headers | wc -l)
    if [ "$NETPOL_COUNT" -gt 0 ]; then
        echo "✓ Found $NETPOL_COUNT Network Policies"
        
        # 检查关键命名空间是否有网络策略
        CRITICAL_NAMESPACES=("kube-system" "kube-public" "default")
        for ns in "${CRITICAL_NAMESPACES[@]}"; do
            NS_NETPOL=$(k8s_check get networkpolicies -n "$ns" --no-headers | wc -l)
            if [ "$NS_NETPOL" -gt 0 ]; then
                echo "✓ Namespace $ns has $NS_NETPOL network policies"
            else
                echo "⚠ Namespace $ns has no network policies"
            fi
        done
    else
        echo "✗ No Network Policies found - network segmentation not implemented"
        exit 1
    fi
    
    # 第四部分：检查 Pod Security Policies（API 检查）
    echo "4. Checking Pod Security Policies..."
    PSP_COUNT=$(k8s_check get psp --no-headers 2>/dev/null | wc -l || echo "0")
    if [ "$PSP_COUNT" -gt 0 ]; then
        echo "✓ Found $PSP_COUNT Pod Security Policies"
    else
        echo "⚠ No Pod Security Policies found (may use Pod Security Standards instead)"
    fi
    
    echo "=== All checks passed ==="
    exit 0

---
# 示例3: etcd 配置和集群状态混合检查
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-etcd-security-cluster-health
  namespace: compliance-system
spec:
  id: "V-242425"
  title: "etcd must be properly secured and cluster must be healthy"
  checkText: "Verify etcd configuration security AND cluster health status"
  severity: "critical"
  checkType: "hybrid"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    # STIG 混合检查：etcd 安全配置 + 集群健康状态
    
    echo "=== STIG Hybrid Check: etcd Security + Cluster Health ==="
    
    # 第一部分：检查 etcd 配置文件（文件系统检查）
    echo "1. Checking etcd configuration..."
    ETCD_MANIFEST="/etc/kubernetes/manifests/etcd.yaml"
    
    if check_host_file "$ETCD_MANIFEST"; then
        echo "✓ etcd manifest exists"
        
        ETCD_CONFIG=$(read_host_file "$ETCD_MANIFEST")
        
        # 检查 TLS 配置
        if echo "$ETCD_CONFIG" | grep -q -- "--cert-file"; then
            echo "✓ etcd TLS certificate configured"
        else
            echo "✗ etcd TLS certificate not configured"
            exit 1
        fi
        
        if echo "$ETCD_CONFIG" | grep -q -- "--key-file"; then
            echo "✓ etcd TLS key configured"
        else
            echo "✗ etcd TLS key not configured"
            exit 1
        fi
        
        # 检查客户端认证
        if echo "$ETCD_CONFIG" | grep -q -- "--client-cert-auth=true"; then
            echo "✓ etcd client certificate authentication enabled"
        else
            echo "✗ etcd client certificate authentication not enabled"
            exit 1
        fi
    else
        echo "✗ etcd manifest not found"
        exit 1
    fi
    
    # 第二部分：检查 etcd 进程（进程检查）
    echo "2. Checking etcd process..."
    if check_host_process "etcd"; then
        echo "✓ etcd process is running"
        
        # 检查 etcd 监听端口
        if check_host_port "2379"; then
            echo "✓ etcd listening on client port 2379"
        else
            echo "✗ etcd not listening on expected client port"
            exit 1
        fi
        
        if check_host_port "2380"; then
            echo "✓ etcd listening on peer port 2380"
        else
            echo "✗ etcd not listening on expected peer port"
            exit 1
        fi
    else
        echo "✗ etcd process not running"
        exit 1
    fi
    
    # 第三部分：检查集群健康状态（API 检查）
    echo "3. Checking cluster health via Kubernetes API..."
    
    # 检查节点状态
    READY_NODES=$(k8s_check get nodes --no-headers | grep " Ready " | wc -l)
    TOTAL_NODES=$(k8s_check get nodes --no-headers | wc -l)
    
    echo "Cluster nodes: $READY_NODES/$TOTAL_NODES ready"
    
    if [ "$READY_NODES" -eq "$TOTAL_NODES" ] && [ "$TOTAL_NODES" -gt 0 ]; then
        echo "✓ All cluster nodes are ready"
    else
        echo "✗ Not all cluster nodes are ready"
        exit 1
    fi
    
    # 检查关键系统 Pod
    SYSTEM_PODS_READY=$(k8s_check get pods -n kube-system --no-headers | grep "Running" | wc -l)
    SYSTEM_PODS_TOTAL=$(k8s_check get pods -n kube-system --no-headers | wc -l)
    
    echo "System pods: $SYSTEM_PODS_READY/$SYSTEM_PODS_TOTAL running"
    
    if [ "$SYSTEM_PODS_READY" -eq "$SYSTEM_PODS_TOTAL" ]; then
        echo "✓ All system pods are running"
    else
        echo "⚠ Some system pods are not running"
        k8s_check get pods -n kube-system | grep -v "Running"
    fi
    
    echo "=== All checks passed ==="
    exit 0

---
# 示例4: 扫描配置 - 使用混合检查
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Scan
metadata:
  name: stig-hybrid-scan
  namespace: compliance-system
spec:
  profile: stig-k8s-hybrid-rules
  scanType: "hybrid"  # 使用混合扫描器
  
  # 混合扫描会：
  # 1. 在每个匹配的节点上创建特权容器
  # 2. 容器同时具备主机文件系统访问和 Kubernetes API 访问能力
  # 3. 支持复杂的 STIG 规则检查（配置文件 + 进程 + API 状态）
