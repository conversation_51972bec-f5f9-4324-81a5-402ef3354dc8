---
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Scan
metadata:
  name: advanced-security-scan
  namespace: compliance-system
  labels:
    app: compliance-operator
    type: security-scan
    environment: production
spec:
  # 指定要使用的合规配置文件
  profile: stig-k8s-v2r2-platform-profile
  
  # 扫描类型: platform（平台级别检查）、node（节点级别检查）或both（两者都执行）
  scanType: both
  
  # 节点选择器，仅在执行node类型扫描时使用，用于选择要扫描的节点
  nodeSelector:
    kubernetes.io/os: linux
    node-role.kubernetes.io/master: ""
  
  # 定期执行扫描的计划（使用cron语法）
  schedule: "0 2 * * *"  # 每天凌晨2点执行
  
  # 保留的历史扫描结果数量
  maxHistoricalResults: 5
  
  # 调试模式（自定义扩展字段，如果实现了的话）
  debug: true
  
  # 超时设置（自定义扩展字段，如果实现了的话）
  timeout: "30m" 