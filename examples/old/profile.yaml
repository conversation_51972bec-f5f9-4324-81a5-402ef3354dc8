---
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Profile
metadata:
  name: custom-security-profile
  namespace: compliance-system
spec:
  title: "Custom Security Profile"
  description: "Custom security compliance profile for Kubernetes"
  rules:
    - name: stig-k8s-admin-conf-file-ownership
    - name: stig-k8s-admin-conf-file-permissions
    - name: stig-k8s-controller-manager-conf-file-permissions
    - name: stig-k8s-kubelet-config-file-permissions
  selections:
    - id: stig-k8s-admin-conf-file-ownership
      selected: true 
    - id: stig-k8s-admin-conf-file-permissions
      selected: true
    - id: stig-k8s-controller-manager-conf-file-permissions
      selected: true
    - id: stig-k8s-kubelet-config-file-permissions
      selected: true