---
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Profile
metadata:
  name: audit-logging-profile
  namespace: compliance-system
spec:
  title: "Kubernetes Audit Logging Profile"
  description: "Compliance profile focusing on audit logging and monitoring capabilities"
  rules:
    # API Server 审计日志配置
    - name: stig-k8s-api-server-audit-log-enabled
    - name: stig-k8s-api-server-audit-log-maxage
    - name: stig-k8s-api-server-audit-log-maxbackup
    - name: stig-k8s-api-server-audit-log-maxsize
    - name: stig-k8s-api-server-audit-policy-configured
    
    # 版本和组件检查
    - name: stig-k8s-version-up-to-date
    - name: stig-k8s-old-components-removed
    
    # 安全策略
    - name: stig-k8s-secrets-not-environment-variables
    - name: stig-k8s-user-resources-dedicated-namespaces
  
  selections:
    # API Server 审计
    - id: stig-k8s-api-server-audit-log-enabled
      selected: true
    - id: stig-k8s-api-server-audit-log-maxage
      selected: true
    - id: stig-k8s-api-server-audit-log-maxbackup
      selected: true
    - id: stig-k8s-api-server-audit-log-maxsize
      selected: true
    - id: stig-k8s-api-server-audit-policy-configured
      selected: true
    
    # 版本检查
    - id: stig-k8s-version-up-to-date
      selected: true
    - id: stig-k8s-old-components-removed
      selected: true
    
    # 安全策略
    - id: stig-k8s-secrets-not-environment-variables
      selected: true
    - id: stig-k8s-user-resources-dedicated-namespaces
      selected: true 