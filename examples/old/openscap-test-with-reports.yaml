apiVersion: compliance-operator.alauda.io/v1alpha1
kind: ProfileBundle
metadata:
  name: ubuntu-openscap-test
  namespace: compliance-system
spec:
  contentImage: registry.alauda.cn:60070/test/compliance/os-content:latest
  contentFile: ssg-ubuntu2204-ds.xml
---
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Profile
metadata:
  name: ubuntu-openscap-test-profile
  namespace: compliance-system
  labels:
    compliance-operator.alauda.io/profile-bundle: ubuntu-openscap-test
spec:
  id: "xccdf_org.ssgproject.content_profile_stig"
  title: "Ubuntu OpenSCAP Test Profile with Report Service"
  description: "Test profile for Ubuntu OpenSCAP scanning with report service integration"
  version: "V2R2"
  dataStream:
    contentImage: registry.alauda.cn:60070/test/compliance/os-content:latest
    contentFile: ssg-ubuntu2204-ds.xml
    profileId: "xccdf_org.ssgproject.content_profile_stig"
    scanType: "node"
---
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Scan
metadata:
  name: ubuntu-openscap-test-scan
  namespace: compliance-system
  labels:
    compliance-operator.alauda.io/scan-type: node
spec:
  scanType: "node"
  profile: "ubuntu-openscap-test-profile"
  nodeSelector:
    kubernetes.io/os: linux 