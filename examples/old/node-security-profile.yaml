---
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Profile
metadata:
  name: node-security-profile
  namespace: compliance-system
spec:
  title: "Kubernetes Node Security Profile"
  description: "Node-level security compliance profile focusing on kubelet and file permissions"
  rules:
    # Kubelet 配置安全
    - name: stig-k8s-kubelet-anonymous-auth-disabled
    - name: stig-k8s-kubelet-authorization-mode
    - name: stig-k8s-kubelet-client-ca-file
    - name: stig-k8s-kubelet-readonly-port-disabled
    - name: stig-k8s-kubelet-streaming-connection-timeout
    - name: stig-k8s-kubelet-tls-cert-file
    - name: stig-k8s-kubelet-tls-private-key-file
    
    # 配置文件权限
    - name: stig-k8s-kubelet-config-file-ownership
    - name: stig-k8s-kubelet-config-file-permissions
    - name: stig-k8s-manifest-file-permissions
    - name: stig-k8s-manifests-owned-by-root
    
    # PKI 安全
    - name: stig-k8s-pki-directory-ownership
    - name: stig-k8s-pki-file-permissions
    - name: stig-k8s-pki-key-file-permissions
    - name: stig-k8s-pki-certificate-file-permissions
  
  selections:
    # Kubelet 配置
    - id: stig-k8s-kubelet-anonymous-auth-disabled
      selected: true
    - id: stig-k8s-kubelet-authorization-mode
      selected: true
    - id: stig-k8s-kubelet-client-ca-file
      selected: true
    - id: stig-k8s-kubelet-readonly-port-disabled
      selected: true
    - id: stig-k8s-kubelet-streaming-connection-timeout
      selected: true
    - id: stig-k8s-kubelet-tls-cert-file
      selected: true
    - id: stig-k8s-kubelet-tls-private-key-file
      selected: true
    
    # 配置文件权限
    - id: stig-k8s-kubelet-config-file-ownership
      selected: true
    - id: stig-k8s-kubelet-config-file-permissions
      selected: true
    - id: stig-k8s-manifest-file-permissions
      selected: true
    - id: stig-k8s-manifests-owned-by-root
      selected: true
    
    # PKI 安全
    - id: stig-k8s-pki-directory-ownership
      selected: true
    - id: stig-k8s-pki-file-permissions
      selected: true
    - id: stig-k8s-pki-key-file-permissions
      selected: true
    - id: stig-k8s-pki-certificate-file-permissions
      selected: true 