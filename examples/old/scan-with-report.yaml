apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Scan
metadata:
  name: stig-k8s-scan-with-report
  namespace: compliance-system
spec:
  profile: stig-k8s-v2r2
  scanType: both
  nodeSelector:
    kubernetes.io/os: linux
  timeout: 30m
  debug: false
---
# After the scan completes, you can view the report using:
# kubectl get scan stig-k8s-scan-with-report -o jsonpath='{.status.reportConfigMap}'
# kubectl get configmap <report-configmap-name> -o jsonpath='{.data.report\.html}' > report.html
# 
# Or use the report-viewer tool:
# go run cmd/report-viewer/main.go -scan stig-k8s-scan-with-report -serve -port 8080