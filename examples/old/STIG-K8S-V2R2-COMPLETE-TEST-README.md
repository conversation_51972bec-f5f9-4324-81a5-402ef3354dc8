# STIG Kubernetes V2R2 Complete Compliance Test

This document provides a comprehensive guide for testing STIG Kubernetes V2R2 compliance using the compliance-operator.

## Overview

The STIG Kubernetes V2R2 compliance test suite includes:
- **65 security rules** covering all STIG requirements
- **2 specialized profiles** (platform and node)
- **Multiple scan configurations** for different testing scenarios
- **Automated test scripts** for easy deployment and execution

## File Structure

```
examples/
├── stig-k8s-v2r2-complete-profile.yaml      # Platform and Node profiles
├── stig-k8s-v2r2-complete-scan.yaml         # Individual scan configurations
├── stig-k8s-v2r2-compliance-suite.yaml      # Comprehensive suite configuration
└── STIG-K8S-V2R2-COMPLETE-TEST-README.md    # This documentation

scripts/
└── test-stig-k8s-v2r2-complete.sh           # Automated test script

stig-k8s-v2r2-rules/                         # All 65 STIG rules
├── stig-k8s-api-server-*.yaml               # 19 API server rules
├── stig-k8s-controller-manager-*.yaml       # 6 controller manager rules
├── stig-k8s-scheduler-*.yaml                # 4 scheduler rules
├── stig-k8s-etcd-*.yaml                     # 8 etcd rules
├── stig-k8s-kubelet-*.yaml                  # 8 kubelet rules
├── stig-k8s-pki-*.yaml                      # 4 PKI rules
└── ... (other security rules)
```

## Rule Categories

### Platform Rules (40 rules)
- **API Server Security** (19 rules): Authentication, authorization, TLS, audit logging
- **Controller Manager** (6 rules): Service accounts, TLS, security settings
- **Scheduler** (2 rules): Secure binding, TLS configuration
- **ETCD** (6 rules): Certificate authentication, TLS settings
- **Platform Security** (7 rules): Network policies, pod security, admission control

### Node Rules (25 rules)
- **File Ownership** (4 rules): Configuration file ownership checks
- **File Permissions** (4 rules): Configuration file permission checks
- **PKI Security** (4 rules): Certificate and key file security
- **ETCD Data** (2 rules): Data directory security
- **Manifest Files** (2 rules): Kubernetes manifest security
- **Kubelet Security** (8 rules): Kubelet configuration and security
- **System Security** (1 rule): Legacy component removal

## Quick Start

### 1. Deploy Everything
```bash
# Make the script executable
chmod +x scripts/test-stig-k8s-v2r2-complete.sh

# Deploy all resources (rules, profiles, scans, compliance suite)
./scripts/test-stig-k8s-v2r2-complete.sh deploy-all
```

### 2. Run Complete Test
```bash
# Run both platform and node scans
./scripts/test-stig-k8s-v2r2-complete.sh test-complete
```

### 3. View Results
```bash
# Show scan results
./scripts/test-stig-k8s-v2r2-complete.sh show-results

# Show compliance suite status
./scripts/test-stig-k8s-v2r2-complete.sh show-suite
```

## Manual Deployment

### Step 1: Deploy Rules
```bash
# Create namespace
kubectl create namespace compliance-system

# Deploy all STIG rules
kubectl apply -f stig-k8s-v2r2-rules/
```

### Step 2: Deploy Profiles
```bash
# Deploy platform and node profiles
kubectl apply -f examples/stig-k8s-v2r2-complete-profile.yaml
```

### Step 3: Deploy Scans
Choose one of the following approaches:

#### Option A: Individual Scans
```bash
# Deploy individual platform and node scans
kubectl apply -f examples/stig-k8s-v2r2-complete-scan.yaml
```

#### Option B: Compliance Suite (Recommended)
```bash
# Deploy comprehensive compliance suite
kubectl apply -f examples/stig-k8s-v2r2-compliance-suite.yaml
```

## Testing Scenarios

### Scenario 1: Platform Security Test
```bash
# Test only platform-level security
./scripts/test-stig-k8s-v2r2-complete.sh test-platform
```

### Scenario 2: Node Security Test
```bash
# Test only node-level security
./scripts/test-stig-k8s-v2r2-complete.sh test-node
```

### Scenario 3: Complete Compliance Test
```bash
# Test all security aspects
./scripts/test-stig-k8s-v2r2-complete.sh test-complete
```

## Monitoring and Results

### Check Scan Status
```bash
# Monitor scan progress
kubectl get scans -n compliance-system -w

# Check specific scan details
kubectl describe scan stig-k8s-v2r2-platform-scan -n compliance-system
```

### View Check Results
```bash
# Get all check results
kubectl get checkresults -n compliance-system

# Get results for specific scan
kubectl get checkresults -n compliance-system -l compliance-operator.alauda.io/scan-name=stig-k8s-v2r2-platform-scan

# View detailed result
kubectl describe checkresult <result-name> -n compliance-system
```

### Compliance Suite Status
```bash
# Check compliance suite status
kubectl get compliancesuites -n compliance-system

# View detailed suite information
kubectl describe compliancesuite stig-k8s-v2r2-suite -n compliance-system
```

## Expected Results

### Typical Compliance Status
Based on testing with standard Kubernetes clusters:

- **Platform Rules**: ~85-90% compliant (34-36 out of 40 rules)
- **Node Rules**: ~80-85% compliant (20-21 out of 25 rules)
- **Overall**: ~82-87% compliant (54-57 out of 65 rules)

### Common Non-Compliant Items
- Audit log retention settings (maxage, maxbackup, maxsize)
- Some TLS cipher suite configurations
- File permission strictness on certain nodes
- Pod security standards implementation

## Troubleshooting

### Common Issues

1. **Scanner Pod Errors**
   ```bash
   # Check scanner pod logs
   kubectl logs -n compliance-system -l compliance-operator.alauda.io/scan-name=<scan-name>
   ```

2. **ConfigMap Creation Issues**
   - Ensure the [fixed scanner image][[memory:7518563734334195838]] is being used
   - Check controller logs for ConfigMap creation errors

3. **Node Scanner Access**
   ```bash
   # Verify node scanner can access host filesystem
   kubectl get pods -n compliance-system -l workload=scanner
   ```

### Debug Mode
Enable debug output by modifying scan configurations:
```yaml
spec:
  debug: true
  showNotApplicable: true
```

## Advanced Configuration

### Custom Node Selection
```yaml
spec:
  nodeSelector:
    kubernetes.io/os: linux
    node-role.kubernetes.io/worker: ""
```

### Custom Tolerations
```yaml
spec:
  tolerations:
    - key: "node-role.kubernetes.io/master"
      operator: "Exists"
      effect: "NoSchedule"
```

### Scheduled Scans
```yaml
spec:
  schedule: "0 2 * * 1"  # Weekly on Monday at 2 AM
```

## Integration with CI/CD

### Jenkins Pipeline Example
```groovy
pipeline {
    agent any
    stages {
        stage('Deploy STIG Tests') {
            steps {
                sh './scripts/test-stig-k8s-v2r2-complete.sh deploy-all'
            }
        }
        stage('Run Compliance Scan') {
            steps {
                sh './scripts/test-stig-k8s-v2r2-complete.sh test-complete'
            }
        }
        stage('Collect Results') {
            steps {
                sh './scripts/test-stig-k8s-v2r2-complete.sh show-results > compliance-results.txt'
                archiveArtifacts 'compliance-results.txt'
            }
        }
    }
}
```

## Cleanup

```bash
# Clean up test resources
./scripts/test-stig-k8s-v2r2-complete.sh cleanup

# Remove all compliance resources
kubectl delete namespace compliance-system
```

## Support and Development

For issues or improvements:
1. Check controller logs: `kubectl logs -n compliance-system deployment/compliance-operator`
2. Verify [development environment setup][[memory:4863470919392137028]]
3. Use the [single rule testing approach][[memory:4555286918222294745]] for debugging specific rules

## Security Considerations

- All scans run with appropriate RBAC permissions
- Node scanners use privileged access only when necessary
- Results are stored securely in ConfigMaps
- No sensitive data is exposed in scan outputs 