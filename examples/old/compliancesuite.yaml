apiVersion: compliance-operator.alauda.io/v1alpha1
kind: ComplianceSuite
metadata:
  name: security-baseline-suite
  namespace: compliance-operator
spec:
  # 定时扫描：每天凌晨2点执行
  schedule: "0 2 * * *"
  
  # 是否自动应用修复措施
  autoApplyRemediations: false
  
  # 扫描配置列表
  scans:
    # 平台合规扫描
    - name: platform-security
      profile: security-baseline
      scanType: platform
      
    # 节点合规扫描 - 主节点
    - name: master-nodes
      profile: node-security
      scanType: node
      nodeSelector:
        node-role.kubernetes.io/master: ""
        
    # 节点合规扫描 - 工作节点
    - name: worker-nodes
      profile: node-security
      scanType: node
      nodeSelector:
        node-role.kubernetes.io/worker: ""
        
    # 混合扫描示例
    - name: comprehensive-scan
      profile: comprehensive-security
      scanType: both
      # 可以为单个扫描设置不同的调度
      schedule: "0 6 * * 0"  # 每周日早上6点

---
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: ComplianceSuite
metadata:
  name: quick-security-check
  namespace: compliance-operator
spec:
  # 一次性扫描（无调度）
  scans:
    - name: quick-platform-check
      profile: basic-security
      scanType: platform
      
    - name: quick-node-check
      profile: basic-security
      scanType: node

---
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: ComplianceSuite
metadata:
  name: weekly-compliance-audit
  namespace: compliance-operator
spec:
  # 每周一早上执行完整的合规审计
  schedule: "0 3 * * 1"
  
  scans:
    # STIG 合规扫描
    - name: stig-platform
      profile: stig-rhel8
      scanType: platform
      
    - name: stig-nodes
      profile: stig-rhel8
      scanType: node
      
    # CIS 基准扫描
    - name: cis-platform
      profile: cis-kubernetes
      scanType: platform
      
    - name: cis-nodes
      profile: cis-rhel8
      scanType: node 