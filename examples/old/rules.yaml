apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-api-server-audit
  namespace: compliance-system
  labels:
    compliance-operator.alauda.io/profile-bundle: k8s-stig-bundle
spec:
  title: "API Server Audit Configuration"
  description: "Ensure API server audit logging is properly configured"
  severity: "high"
  checkType: "platform"
  checkScript: |
    #!/bin/bash
    # Check if API server has audit logging enabled
    if kubectl get pod -n kube-system -l component=kube-apiserver -o yaml | grep -q "audit-log-path"; then
        echo "PASS: API Server audit logging is enabled"
        exit 0
    else
        echo "FAIL: API Server audit logging is not enabled"
        exit 1
    fi
  instructions: |
    Configure audit logging in API server manifest by adding:
    --audit-log-path=/var/log/audit.log
    --audit-log-maxage=30
    --audit-log-maxbackup=3
    --audit-log-maxsize=100
  availableFixes:
    - platform: "kubernetes"
      disruption: "medium"
  stig:
    id: "V-242376"
    cat: "CAT-I"
---
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-api-server-tls
  namespace: compliance-system
  labels:
    compliance-operator.alauda.io/profile-bundle: k8s-stig-bundle
spec:
  title: "API Server TLS Configuration"
  description: "Ensure API server uses secure TLS settings"
  severity: "high"
  checkType: "platform"
  checkScript: |
    #!/bin/bash
    # Check if API server has proper TLS configuration
    api_server_pod=$(kubectl get pod -n kube-system -l component=kube-apiserver -o name | head -1)
    if kubectl get $api_server_pod -n kube-system -o yaml | grep -q "tls-cert-file"; then
        echo "PASS: API Server TLS certificate is configured"
        exit 0
    else
        echo "FAIL: API Server TLS certificate is not configured"
        exit 1
    fi
  instructions: |
    Configure TLS certificates for API server by adding:
    --tls-cert-file=/etc/kubernetes/pki/apiserver.crt
    --tls-private-key-file=/etc/kubernetes/pki/apiserver.key
  stig:
    id: "V-242377"
    cat: "CAT-I"
---
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-node-kubelet-config
  namespace: compliance-system
  labels:
    compliance-operator.alauda.io/profile-bundle: k8s-stig-bundle
spec:
  title: "Kubelet Configuration Security"
  description: "Ensure kubelet is configured with secure settings"
  severity: "medium"
  checkType: "node"
  checkScript: |
    #!/bin/bash
    # Check kubelet configuration on the node
    if [ -f /var/lib/kubelet/config.yaml ]; then
        if grep -q "authentication:" /var/lib/kubelet/config.yaml; then
            echo "PASS: Kubelet authentication is configured"
            exit 0
        else
            echo "FAIL: Kubelet authentication is not configured"
            exit 1
        fi
    else
        echo "FAIL: Kubelet configuration file not found"
        exit 1
    fi
  instructions: |
    Configure kubelet authentication in /var/lib/kubelet/config.yaml:
    authentication:
      webhook:
        enabled: true
      anonymous:
        enabled: false
  stig:
    id: "V-242400"
    cat: "CAT-II"
---
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: custom-pod-security-standards
  namespace: compliance-system
spec:
  title: "Pod Security Standards"
  description: "Ensure pods comply with security standards"
  severity: "medium"
  checkType: "platform"
  checkScript: |
    #!/bin/bash
    # Check for Pod Security Standards
    if kubectl get ns demo-ns01 -o yaml | grep -q "pod-security.kubernetes.io"; then
        echo "PASS: Pod Security Standards are enabled"
        exit 0
    else
        echo "FAIL: Pod Security Standards are not enabled"
        exit 1
    fi
  instructions: |
    Enable Pod Security Standards by adding labels to namespaces:
    pod-security.kubernetes.io/enforce: restricted
    pod-security.kubernetes.io/audit: restricted
    pod-security.kubernetes.io/warn: restricted 