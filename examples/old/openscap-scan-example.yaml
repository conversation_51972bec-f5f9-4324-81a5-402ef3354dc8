apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Scan
metadata:
  name: ubuntu-openscap-scan
  namespace: compliance-system
spec:
  profile: ubuntu-stig-openscap
  scanType: node
  nodeSelector:
    kubernetes.io/os: linux
# ---
# # Example: SUSE Linux Micro scan
# apiVersion: compliance-operator.alauda.io/v1alpha1
# kind: Scan
# metadata:
#   name: slmicro-openscap-scan
#   namespace: compliance-system
# spec:
#   profile: slmicro-stig-openscap
#   scanType: node
#   nodeSelector:
#     kubernetes.io/os: linux 