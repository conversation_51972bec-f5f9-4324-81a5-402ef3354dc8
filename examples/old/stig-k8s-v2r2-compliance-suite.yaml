---
# STIG Kubernetes V2R2 ComplianceSuite
# Comprehensive compliance suite that manages both platform and node scans
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: ComplianceSuite
metadata:
  name: stig-k8s-v2r2-suite
  namespace: compliance-system
  labels:
    compliance-operator.alauda.io/suite-type: complete
    compliance-operator.alauda.io/standard: stig
    compliance-operator.alauda.io/version: v2r2
spec:
  # Schedule for the entire suite
  schedule: "0 1 * * 1"  # Weekly on Monday at 1 AM
  
  # Scan configurations
  scans:
    - name: "platform-security"
      profile: stig-k8s-v2r2-platform
      scanType: "platform"
      
    - name: "node-security"  
      profile: stig-k8s-v2r2-node
      scanType: "node"
      nodeSelector:
        kubernetes.io/os: linux 