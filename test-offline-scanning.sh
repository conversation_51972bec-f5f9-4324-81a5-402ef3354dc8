#!/bin/bash

set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

log_error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

log_success() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] SUCCESS: $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

# Configuration
CONTENT_IMAGE_TAG="compliance-content:offline-test"
SCANNER_IMAGE_TAG="compliance-scanner:offline-test"
TEST_CONTENT="ssg-slmicro5-ds.xml"
TEST_PROFILE="xccdf_org.ssgproject.content_profile_standard"

# Build content image
build_content_image() {
    log "Building content image with offline dependencies..."
    
    cd images/content
    
    if docker build -t "$CONTENT_IMAGE_TAG" .; then
        log_success "Content image built successfully: $CONTENT_IMAGE_TAG"
    else
        log_error "Failed to build content image"
        return 1
    fi
    
    cd - > /dev/null
}

# Build scanner image
build_scanner_image() {
    log "Building scanner image..."
    
    cd images/openscap-scanner
    
    if docker build -t "$SCANNER_IMAGE_TAG" .; then
        log_success "Scanner image built successfully: $SCANNER_IMAGE_TAG"
    else
        log_error "Failed to build scanner image"
        return 1
    fi
    
    cd - > /dev/null
}

# Test content image
test_content_image() {
    log "Testing content image..."
    
    log "Checking content image contents..."
    if docker run --rm "$CONTENT_IMAGE_TAG" ls -la /content/; then
        log_success "Content image listing successful"
    else
        log_error "Failed to list content image contents"
        return 1
    fi
    
    log "Checking for offline datastreams..."
    if docker run --rm "$CONTENT_IMAGE_TAG" sh -c 'ls -la /content/*offline*.xml 2>/dev/null || echo "No offline datastreams found"'; then
        log_success "Offline datastream check completed"
    else
        log_warning "Could not check for offline datastreams"
    fi
    
    log "Checking for SUSE OVAL dependencies..."
    if docker run --rm "$CONTENT_IMAGE_TAG" sh -c 'ls -la /content/suse.linux.enterprise.micro.5-patch.xml 2>/dev/null || echo "SUSE OVAL dependency not found"'; then
        log_success "SUSE OVAL dependency check completed"
    else
        log_warning "Could not check for SUSE OVAL dependencies"
    fi
}

# Test offline datastream info
test_datastream_info() {
    log "Testing datastream info without --fetch-remote-resources..."

    # Create a temporary container to test oscap info
    local container_name="test-oscap-info-$$"

    # Create a shared volume
    log "Creating shared volume..."
    if ! docker volume create "${container_name}-vol"; then
        log_error "Failed to create shared volume"
        return 1
    fi

    # Copy content to shared volume using a temporary container
    log "Copying content to shared volume..."
    if ! docker run --rm -v "${container_name}-vol:/shared-content" -v "$PWD/images/content/ds:/source:ro" alpine:3.18 sh -c "cp -r /source/* /shared-content/"; then
        log_error "Failed to copy content to shared volume"
        docker volume rm "${container_name}-vol" > /dev/null 2>&1 || true
        return 1
    fi

    # Also copy the downloaded dependencies from the content image
    log "Copying dependencies from content image..."
    if ! docker run --rm -v "${container_name}-vol:/shared-content" "$CONTENT_IMAGE_TAG" sh -c "cp /content/*.xml /shared-content/ 2>/dev/null || true"; then
        log_warning "Could not copy all dependencies from content image"
    fi
    
    # Test with scanner container
    log "Testing oscap info with offline datastream..."
    local test_result=0
    
    if docker run --rm \
        --volumes-from "${container_name}-data" \
        "$SCANNER_IMAGE_TAG" \
        sh -c "
            cd /shared-content
            echo '=== Available files ==='
            ls -la
            echo '=== Testing original datastream ==='
            oscap info $TEST_CONTENT || echo 'Original datastream failed (expected)'
            echo '=== Testing offline datastream ==='
            if ls *offline*.xml 1> /dev/null 2>&1; then
                for offline_ds in *offline*.xml; do
                    echo \"Testing: \$offline_ds\"
                    if oscap info \"\$offline_ds\"; then
                        echo \"SUCCESS: \$offline_ds works without --fetch-remote-resources\"
                    else
                        echo \"FAILED: \$offline_ds still has issues\"
                    fi
                done
            else
                echo 'No offline datastreams found'
            fi
        "; then
        log_success "Datastream info test completed"
    else
        log_error "Datastream info test failed"
        test_result=1
    fi
    
    # Cleanup
    docker rm -f "${container_name}-content" "${container_name}-data" > /dev/null 2>&1 || true
    
    return $test_result
}

# Test profile listing
test_profile_listing() {
    log "Testing profile listing..."
    
    local container_name="test-profiles-$$"
    
    # Start content container
    if ! docker run -d --name "${container_name}-content" "$CONTENT_IMAGE_TAG" sleep 3600; then
        log_error "Failed to start content container"
        return 1
    fi
    
    # Create shared volume and copy content
    docker create --name "${container_name}-data" -v /shared-content alpine:3.18
    docker cp "${container_name}-content:/content/." "${container_name}-data:/shared-content/"
    
    # Test profile listing
    local test_result=0
    
    if docker run --rm \
        --volumes-from "${container_name}-data" \
        "$SCANNER_IMAGE_TAG" \
        sh -c "
            cd /shared-content
            echo '=== Testing profile listing ==='
            for ds_file in *offline*.xml; do
                if [[ -f \"\$ds_file\" ]]; then
                    echo \"Listing profiles in: \$ds_file\"
                    oscap info --profiles \"\$ds_file\" || echo \"Profile listing failed for \$ds_file\"
                fi
            done
        "; then
        log_success "Profile listing test completed"
    else
        log_error "Profile listing test failed"
        test_result=1
    fi
    
    # Cleanup
    docker rm -f "${container_name}-content" "${container_name}-data" > /dev/null 2>&1 || true
    
    return $test_result
}

# Main test execution
main() {
    log "Starting offline scanning functionality test..."
    
    # Check if Docker is available
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not available"
        exit 1
    fi
    
    # Build images
    build_content_image
    build_scanner_image
    
    # Test content image
    test_content_image
    
    # Test datastream info
    test_datastream_info
    
    # Test profile listing
    test_profile_listing
    
    log_success "Offline scanning functionality test completed!"
    log "Next steps:"
    log "1. Test with actual scanning using a profile"
    log "2. Verify that no network requests are made during scanning"
    log "3. Compare results with online scanning to ensure accuracy"
}

# Execute main function
main "$@"
