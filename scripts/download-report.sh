#!/bin/bash

# Unified Report Download Script
# This script provides multiple ways to download compliance scan reports

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# Default values
NAMESPACE="compliance-system"
OUTPUT_DIR="./reports"
METHOD="cli"
API_SERVER_URL="http://localhost:8080"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

usage() {
    echo "Usage: $0 [OPTIONS] <scan-name>"
    echo ""
    echo "Download compliance scan reports using unified report download service"
    echo ""
    echo "Options:"
    echo "  -n, --namespace NAMESPACE    Kubernetes namespace (default: compliance-system)"
    echo "  -o, --output DIR            Output directory (default: ./reports)"
    echo "  -m, --method METHOD         Download method: cli|api (default: cli)"
    echo "  -u, --url URL              API server URL for api method (default: http://localhost:8080)"
    echo "  -l, --list                 List available scans"
    echo "  -h, --help                 Show this help message"
    echo ""
    echo "Methods:"
    echo "  cli  - Use the report-downloader CLI tool (requires kubeconfig)"
    echo "  api  - Use the report API server (requires running server)"
    echo ""
    echo "Examples:"
    echo "  # Download using CLI tool"
    echo "  $0 stig-k8s-v2r2-node-scan"
    echo ""
    echo "  # Download using API server"
    echo "  $0 -m api stig-k8s-v2r2-node-scan"
    echo ""
    echo "  # List available scans"
    echo "  $0 -l"
    echo ""
    echo "  # Download to specific directory"
    echo "  $0 -o /tmp/reports stig-k8s-v2r2-node-scan"
}

log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

list_scans() {
    log_info "Listing available scans in namespace '$NAMESPACE'..."
    
    if [[ "$METHOD" == "api" ]]; then
        # Use API server
        if ! curl -s "$API_SERVER_URL/health" > /dev/null 2>&1; then
            log_error "API server is not accessible at $API_SERVER_URL"
            log_info "Start the API server with: go run cmd/report-api-server/main.go"
            exit 1
        fi
        
        echo ""
        echo "Available scans:"
        curl -s "$API_SERVER_URL/scans" | jq -r '.scans[] | "  \(.name) (\(.phase)) - \(.profile) - \(.timestamp // "No results")"'
    else
        # Use kubectl
        echo ""
        echo "Available scans:"
        kubectl get scans -n "$NAMESPACE" -o custom-columns="NAME:.metadata.name,PROFILE:.spec.profile,PHASE:.status.phase,RESULT:.status.result,LAST_SCAN:.status.latestResult.timestamp" --no-headers | while read line; do
            echo "  $line"
        done
    fi
}

download_report_cli() {
    local scan_name="$1"
    local output_file="$OUTPUT_DIR/${scan_name}-report.zip"
    
    log_info "Downloading report for scan '$scan_name' using CLI tool..."
    
    # Ensure output directory exists
    mkdir -p "$OUTPUT_DIR"
    
    # Build and run the CLI tool
    cd "$PROJECT_ROOT"
    go run cmd/report-downloader/main.go \
        -scan "$scan_name" \
        -namespace "$NAMESPACE" \
        -output "$output_file"
    
    log_success "Report downloaded: $output_file"
    
    # Show file info
    if [[ -f "$output_file" ]]; then
        local size=$(du -h "$output_file" | cut -f1)
        log_info "File size: $size"
        log_info "Extract with: unzip '$output_file' -d '${OUTPUT_DIR}/${scan_name}-extracted/'"
    fi
}

download_report_api() {
    local scan_name="$1"
    local output_file="$OUTPUT_DIR/${scan_name}-report.zip"
    
    log_info "Downloading report for scan '$scan_name' using API server..."
    
    # Check if API server is accessible
    if ! curl -s "$API_SERVER_URL/health" > /dev/null 2>&1; then
        log_error "API server is not accessible at $API_SERVER_URL"
        log_info "Start the API server with: go run cmd/report-api-server/main.go"
        exit 1
    fi
    
    # Ensure output directory exists
    mkdir -p "$OUTPUT_DIR"
    
    # Download using curl
    log_info "Downloading from $API_SERVER_URL/download/$scan_name..."
    if curl -f -o "$output_file" "$API_SERVER_URL/download/$scan_name"; then
        log_success "Report downloaded: $output_file"
        
        # Show file info
        if [[ -f "$output_file" ]]; then
            local size=$(du -h "$output_file" | cut -f1)
            log_info "File size: $size"
            log_info "Extract with: unzip '$output_file' -d '${OUTPUT_DIR}/${scan_name}-extracted/'"
        fi
    else
        log_error "Failed to download report"
        exit 1
    fi
}

# Parse command line arguments
LIST_SCANS=false
SCAN_NAME=""

while [[ $# -gt 0 ]]; do
    case $1 in
        -n|--namespace)
            NAMESPACE="$2"
            shift 2
            ;;
        -o|--output)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        -m|--method)
            METHOD="$2"
            shift 2
            ;;
        -u|--url)
            API_SERVER_URL="$2"
            shift 2
            ;;
        -l|--list)
            LIST_SCANS=true
            shift
            ;;
        -h|--help)
            usage
            exit 0
            ;;
        -*)
            log_error "Unknown option: $1"
            usage
            exit 1
            ;;
        *)
            if [[ -z "$SCAN_NAME" ]]; then
                SCAN_NAME="$1"
            else
                log_error "Multiple scan names provided"
                usage
                exit 1
            fi
            shift
            ;;
    esac
done

# Validate method
if [[ "$METHOD" != "cli" && "$METHOD" != "api" ]]; then
    log_error "Invalid method: $METHOD. Must be 'cli' or 'api'"
    exit 1
fi

# Handle list scans
if [[ "$LIST_SCANS" == "true" ]]; then
    list_scans
    exit 0
fi

# Validate scan name
if [[ -z "$SCAN_NAME" ]]; then
    log_error "Scan name is required"
    usage
    exit 1
fi

# Download report
case "$METHOD" in
    cli)
        download_report_cli "$SCAN_NAME"
        ;;
    api)
        download_report_api "$SCAN_NAME"
        ;;
esac

log_success "Report download completed!"
