#!/bin/bash

# Test script for unified report download functionality
# This script tests the new unified report download service

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test configuration
NAMESPACE="compliance-system"
TEST_SCAN_NAME="test-unified-report-scan"
API_SERVER_PORT="8081"
API_SERVER_PID=""

log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

cleanup() {
    log_info "Cleaning up test environment..."
    
    # Stop API server if running
    if [[ -n "$API_SERVER_PID" ]]; then
        log_info "Stopping API server (PID: $API_SERVER_PID)..."
        kill $API_SERVER_PID 2>/dev/null || true
        wait $API_SERVER_PID 2>/dev/null || true
    fi
    
    # Clean up test files
    rm -rf /tmp/test-reports
    
    log_info "Cleanup completed"
}

# Set up cleanup trap
trap cleanup EXIT

test_compilation() {
    log_info "Testing compilation of all components..."
    
    cd "$PROJECT_ROOT"
    
    # Test CLI tool compilation
    log_info "Compiling CLI tool..."
    if go build -o /tmp/report-downloader cmd/report-downloader/main.go; then
        log_success "CLI tool compiled successfully"
    else
        log_error "CLI tool compilation failed"
        return 1
    fi
    
    # Test API server compilation
    log_info "Compiling API server..."
    if go build -o /tmp/report-api-server cmd/report-api-server/main.go; then
        log_success "API server compiled successfully"
    else
        log_error "API server compilation failed"
        return 1
    fi
    
    # Test main controller compilation
    log_info "Compiling main controller..."
    if go build -o /tmp/compliance-operator cmd/manager/main.go; then
        log_success "Main controller compiled successfully"
    else
        log_error "Main controller compilation failed"
        return 1
    fi
    
    log_success "All components compiled successfully"
}

test_api_server() {
    log_info "Testing API server functionality..."
    
    # Start API server in background
    log_info "Starting API server on port $API_SERVER_PORT..."
    /tmp/report-api-server -port "$API_SERVER_PORT" -namespace "$NAMESPACE" &
    API_SERVER_PID=$!
    
    # Wait for server to start
    sleep 3
    
    # Test health endpoint
    log_info "Testing health endpoint..."
    if curl -s "http://localhost:$API_SERVER_PORT/health" > /dev/null; then
        log_success "Health endpoint is accessible"
    else
        log_error "Health endpoint is not accessible"
        return 1
    fi
    
    # Test scans endpoint
    log_info "Testing scans endpoint..."
    if curl -s "http://localhost:$API_SERVER_PORT/scans" > /dev/null; then
        log_success "Scans endpoint is accessible"
    else
        log_error "Scans endpoint is not accessible"
        return 1
    fi
    
    # Test web interface
    log_info "Testing web interface..."
    if curl -s "http://localhost:$API_SERVER_PORT/" | grep -q "Compliance Report API"; then
        log_success "Web interface is working"
    else
        log_error "Web interface is not working"
        return 1
    fi
    
    log_success "API server tests passed"
}

test_cli_tool() {
    log_info "Testing CLI tool functionality..."
    
    # Test help output
    log_info "Testing CLI help output..."
    if /tmp/report-downloader 2>&1 | grep -q "Usage:"; then
        log_success "CLI help output is correct"
    else
        log_error "CLI help output is incorrect"
        return 1
    fi
    
    log_success "CLI tool tests passed"
}

test_download_script() {
    log_info "Testing download script functionality..."
    
    # Make script executable
    chmod +x "$PROJECT_ROOT/scripts/download-report.sh"
    
    # Test help output
    log_info "Testing script help output..."
    if "$PROJECT_ROOT/scripts/download-report.sh" -h | grep -q "Usage:"; then
        log_success "Script help output is correct"
    else
        log_error "Script help output is incorrect"
        return 1
    fi
    
    # Test list functionality (should work even without scans)
    log_info "Testing script list functionality..."
    if "$PROJECT_ROOT/scripts/download-report.sh" -l; then
        log_success "Script list functionality works"
    else
        log_warning "Script list functionality failed (may be expected if no scans exist)"
    fi
    
    log_success "Download script tests passed"
}

test_integration() {
    log_info "Testing integration between components..."
    
    # Test that API server can list scans
    log_info "Testing scan listing via API..."
    local scans_response=$(curl -s "http://localhost:$API_SERVER_PORT/scans")
    if echo "$scans_response" | jq -e '.scans' > /dev/null 2>&1; then
        log_success "API returns valid scan list JSON"
        
        # Show available scans
        local scan_count=$(echo "$scans_response" | jq '.scans | length')
        log_info "Found $scan_count scans in namespace $NAMESPACE"
        
        if [[ $scan_count -gt 0 ]]; then
            log_info "Available scans:"
            echo "$scans_response" | jq -r '.scans[] | "  - \(.name) (\(.phase)) - \(.profile)"'
        else
            log_warning "No scans found in namespace $NAMESPACE"
        fi
    else
        log_error "API does not return valid JSON"
        return 1
    fi
    
    log_success "Integration tests passed"
}

show_usage_examples() {
    log_info "Usage examples for the unified report download system:"
    echo ""
    echo "1. Using the CLI tool:"
    echo "   /tmp/report-downloader -scan <scan-name>"
    echo ""
    echo "2. Using the API server:"
    echo "   curl http://localhost:$API_SERVER_PORT/scans"
    echo "   curl -O http://localhost:$API_SERVER_PORT/download/<scan-name>"
    echo ""
    echo "3. Using the convenience script:"
    echo "   $PROJECT_ROOT/scripts/download-report.sh <scan-name>"
    echo "   $PROJECT_ROOT/scripts/download-report.sh -m api <scan-name>"
    echo ""
    echo "4. Web interface:"
    echo "   Open http://localhost:$API_SERVER_PORT/ in your browser"
    echo ""
}

main() {
    log_info "Starting unified report download functionality tests..."
    echo ""
    
    # Run tests
    test_compilation
    echo ""
    
    test_cli_tool
    echo ""
    
    test_api_server
    echo ""
    
    test_download_script
    echo ""
    
    test_integration
    echo ""
    
    show_usage_examples
    
    log_success "All tests passed! 🎉"
    log_info "The unified report download system is ready to use."
    echo ""
    log_info "API server is still running on http://localhost:$API_SERVER_PORT"
    log_info "Press Ctrl+C to stop the server and exit."
    
    # Keep the API server running for manual testing
    wait $API_SERVER_PID
}

# Check if jq is available
if ! command -v jq &> /dev/null; then
    log_warning "jq is not installed. Some tests may not work properly."
    log_info "Install jq with: brew install jq (macOS) or apt-get install jq (Ubuntu)"
fi

# Check if curl is available
if ! command -v curl &> /dev/null; then
    log_error "curl is required but not installed"
    exit 1
fi

main "$@"
