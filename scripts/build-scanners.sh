#!/bin/bash

# 构建所有扫描器镜像的脚本

set -euo pipefail

# 配置
REGISTRY="${REGISTRY:-registry.alauda.cn:60070/test/compliance}"
TAG="${TAG:-latest}"

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $*"
}

# 构建函数
build_image() {
    local name=$1
    local dockerfile_path=$2
    local image_tag="${REGISTRY}/${name}:${TAG}"
    
    log "Building $name scanner image..."
    log "Dockerfile: $dockerfile_path"
    log "Image tag: $image_tag"
    
    if docker build -t "$image_tag" "$dockerfile_path"; then
        log "✅ Successfully built $name scanner image"
        return 0
    else
        log "❌ Failed to build $name scanner image"
        return 1
    fi
}

# 推送函数
push_image() {
    local name=$1
    local image_tag="${REGISTRY}/${name}:${TAG}"
    
    log "Pushing $name scanner image..."
    
    if docker push "$image_tag"; then
        log "✅ Successfully pushed $name scanner image"
        return 0
    else
        log "❌ Failed to push $name scanner image"
        return 1
    fi
}

# 主函数
main() {
    log "Starting scanner images build process"
    log "Registry: $REGISTRY"
    log "Tag: $TAG"
    
    # 检查 Docker 是否可用
    if ! command -v docker &> /dev/null; then
        log "❌ Docker not found"
        exit 1
    fi
    
    # 构建 Platform Scanner
    log "=== Building Platform Scanner ==="
    if build_image "platform-scanner" "images/platform-scanner"; then
        log "Platform scanner build completed"
    else
        log "Platform scanner build failed"
        exit 1
    fi
    
    # 构建 Node Scanner
    log "=== Building Node Scanner ==="
    if build_image "node-scanner" "images/node-scanner"; then
        log "Node scanner build completed"
    else
        log "Node scanner build failed"
        exit 1
    fi
    
    # 询问是否推送镜像
    if [ "${PUSH_IMAGES:-}" = "true" ]; then
        log "=== Pushing Images ==="
        
        log "Pushing platform-scanner..."
        push_image "platform-scanner"
        
        log "Pushing node-scanner..."
        push_image "node-scanner"
        
        log "✅ All images pushed successfully"
    else
        log "Images built successfully. Set PUSH_IMAGES=true to push to registry."
    fi
    
    log "=== Build Summary ==="
    log "Platform Scanner: ${REGISTRY}/platform-scanner:${TAG}"
    log "Node Scanner: ${REGISTRY}/node-scanner:${TAG}"
    log "Build process completed successfully"
}

# 执行主函数
main "$@"
